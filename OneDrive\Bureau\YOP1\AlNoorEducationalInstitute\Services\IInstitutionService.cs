using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Drawing;
using AlNoorEducationalInstitute.Models.Institution;

namespace AlNoorEducationalInstitute.Services
{
    /// <summary>
    /// واجهة خدمة إدارة ملف المؤسسة والشعار
    /// Institution profile and logo management service interface
    /// </summary>
    public interface IInstitutionService
    {
        // إدارة ملف المؤسسة
        Task<InstitutionProfile> GetInstitutionProfileAsync();
        Task<bool> UpdateInstitutionProfileAsync(InstitutionProfile profile);
        Task<bool> CreateInstitutionProfileAsync(InstitutionProfile profile);
        Task<bool> ResetInstitutionProfileAsync();

        // إدارة الشعار
        Task<LogoUploadResult> UploadLogoAsync(string filePath, int uploadedByUserId);
        Task<LogoUploadResult> UploadLogoAsync(byte[] imageData, string fileName, string fileType, int uploadedByUserId);
        Task<bool> DeleteLogoAsync(int deletedByUserId);
        Task<LogoInfo?> GetLogoInfoAsync();
        Task<byte[]?> GetLogoImageAsync();
        Task<byte[]?> GetLogoImageAsync(LogoSize size);
        Task<Image?> GetLogoAsImageAsync();
        Task<Image?> GetLogoAsImageAsync(LogoSize size);

        // التحقق من صحة الشعار
        Task<List<string>> ValidateLogoFileAsync(string filePath);
        Task<List<string>> ValidateLogoDataAsync(byte[] imageData, string fileName, string fileType);
        Task<bool> IsValidLogoFileAsync(string filePath);
        Task<bool> IsValidLogoDataAsync(byte[] imageData, string fileType);

        // معالجة الصور
        Task<byte[]> ResizeLogoAsync(byte[] originalImage, int width, int height, bool maintainAspectRatio = true);
        Task<byte[]> ResizeLogoAsync(byte[] originalImage, LogoSize targetSize);
        Task<byte[]> CompressLogoAsync(byte[] originalImage, int quality = 85);
        Task<byte[]> ConvertLogoFormatAsync(byte[] originalImage, string targetFormat);

        // إعدادات الشعار
        Task<LogoSettings> GetLogoSettingsAsync();
        Task<bool> UpdateLogoSettingsAsync(LogoSettings settings);
        Task<LogoSettings> GetDefaultLogoSettingsAsync();

        // معلومات الشعار
        Task<bool> HasLogoAsync();
        Task<DateTime?> GetLogoUploadDateAsync();
        Task<string> GetLogoFileNameAsync();
        Task<string> GetLogoFileTypeAsync();
        Task<long> GetLogoFileSizeAsync();
        Task<Size> GetLogoDimensionsAsync();

        // تصدير واستيراد
        Task<string> ExportLogoAsync(string exportPath);
        Task<bool> ImportLogoAsync(string importPath, int importedByUserId);
        Task<byte[]> GetLogoForExportAsync();
        Task<string> GetLogoBase64Async();
        Task<bool> ImportLogoFromBase64Async(string base64Data, string fileName, int importedByUserId);

        // النسخ الاحتياطي والاستعادة
        Task<bool> BackupLogoAsync(string backupPath);
        Task<bool> RestoreLogoAsync(string backupPath, int restoredByUserId);
        Task<bool> HasLogoBackupAsync(string backupPath);

        // التقارير والإحصائيات
        Task<object> GetLogoUsageStatisticsAsync();
        Task<List<string>> GetLogoUsageLocationsAsync();
        Task<object> GetLogoHistoryAsync();
        Task<bool> LogLogoOperationAsync(string operation, string details, int userId);

        // التكامل مع التقارير
        Task<byte[]?> GetLogoForReportsAsync();
        Task<byte[]?> GetLogoForInvoicesAsync();
        Task<byte[]?> GetLogoForCertificatesAsync();
        Task<byte[]?> GetLogoForLettersAsync();
        Task<byte[]?> GetLogoForLoginScreenAsync();
        Task<byte[]?> GetLogoForDashboardAsync();

        // إعدادات العرض
        Task<bool> IsLogoEnabledForReportsAsync();
        Task<bool> IsLogoEnabledForInvoicesAsync();
        Task<bool> IsLogoEnabledForCertificatesAsync();
        Task<bool> IsLogoEnabledForLettersAsync();
        Task<bool> IsLogoEnabledForLoginScreenAsync();
        Task<bool> IsLogoEnabledForDashboardAsync();

        Task<bool> SetLogoEnabledForReportsAsync(bool enabled);
        Task<bool> SetLogoEnabledForInvoicesAsync(bool enabled);
        Task<bool> SetLogoEnabledForCertificatesAsync(bool enabled);
        Task<bool> SetLogoEnabledForLettersAsync(bool enabled);
        Task<bool> SetLogoEnabledForLoginScreenAsync(bool enabled);
        Task<bool> SetLogoEnabledForDashboardAsync(bool enabled);

        // الألوان والهوية البصرية
        Task<string> GetPrimaryColorAsync();
        Task<string> GetSecondaryColorAsync();
        Task<string> GetTextColorAsync();
        Task<bool> UpdateColorsAsync(string primaryColor, string secondaryColor, string textColor);
        Task<Dictionary<string, string>> GetBrandColorsAsync();

        // معلومات الاتصال
        Task<string> GetInstitutionNameAsync();
        Task<string> GetInstitutionNameEnAsync();
        Task<string> GetAddressAsync();
        Task<string> GetPhoneNumberAsync();
        Task<string> GetEmailAsync();
        Task<string> GetWebsiteAsync();

        // معلومات التراخيص
        Task<string> GetLicenseNumberAsync();
        Task<DateTime?> GetLicenseExpiryDateAsync();
        Task<bool> IsLicenseValidAsync();
        Task<int> GetDaysUntilLicenseExpiryAsync();
        Task<bool> UpdateLicenseInfoAsync(string licenseNumber, DateTime? issueDate, DateTime? expiryDate, string issuedBy);

        // التحقق من الصحة
        Task<List<string>> ValidateInstitutionProfileAsync(InstitutionProfile profile);
        Task<bool> IsInstitutionProfileCompleteAsync();
        Task<List<string>> GetMissingProfileFieldsAsync();

        // الأمان والصلاحيات
        Task<bool> CanUserManageLogoAsync(int userId);
        Task<bool> CanUserViewLogoAsync(int userId);
        Task<bool> CanUserManageProfileAsync(int userId);
        Task<List<string>> GetUserPermissionsAsync(int userId);

        // التنبيهات والإشعارات
        Task SendLogoUploadNotificationAsync(int userId, bool success, string message = "");
        Task SendLicenseExpiryNotificationAsync();
        Task<bool> ShouldNotifyLicenseExpiryAsync();
        Task<List<string>> GetNotificationRecipientsAsync();

        // الصيانة والتنظيف
        Task CleanupOldLogoVersionsAsync();
        Task OptimizeLogoStorageAsync();
        Task<object> GetStorageUsageAsync();
        Task<bool> RepairCorruptedLogoAsync();

        // التدقيق والسجلات
        Task<List<object>> GetLogoAuditLogAsync(DateTime startDate, DateTime endDate);
        Task<List<object>> GetProfileAuditLogAsync(DateTime startDate, DateTime endDate);
        Task LogProfileChangeAsync(string fieldName, string oldValue, string newValue, int userId);

        // التصدير للأنظمة الخارجية
        Task<object> ExportProfileForAPIAsync();
        Task<object> ExportLogoForAPIAsync();
        Task<string> GetLogoUrlAsync(); // للاستخدام في الويب
        Task<bool> GenerateLogoThumbnailsAsync();

        // إعدادات متقدمة
        Task<bool> EnableWatermarkAsync(bool enable);
        Task<bool> IsWatermarkEnabledAsync();
        Task<byte[]?> GetWatermarkedLogoAsync();
        Task<bool> SetLogoTransparencyAsync(float transparency);
        Task<float> GetLogoTransparencyAsync();

        // التكامل مع الطباعة
        Task<Image?> GetLogoForPrintingAsync(int dpi = 300);
        Task<byte[]?> GetLogoForPrintingAsync(int width, int height, int dpi = 300);
        Task<bool> OptimizeLogoForPrintingAsync();

        // إحصائيات الاستخدام
        Task<int> GetLogoViewCountAsync();
        Task<int> GetLogoDownloadCountAsync();
        Task IncrementLogoViewCountAsync();
        Task IncrementLogoDownloadCountAsync();
        Task<object> GetLogoUsageAnalyticsAsync(DateTime startDate, DateTime endDate);

        // التحديثات التلقائية
        Task<bool> AutoUpdateLogoInReportsAsync();
        Task<bool> AutoUpdateLogoInDocumentsAsync();
        Task<bool> RefreshLogoInAllApplicationsAsync();
        Task NotifyLogoUpdateToAllUsersAsync();
    }
}
