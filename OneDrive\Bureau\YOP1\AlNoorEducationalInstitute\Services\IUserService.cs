using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AlNoorEducationalInstitute.Models;

namespace AlNoorEducationalInstitute.Services
{
    /// <summary>
    /// واجهة خدمة إدارة المستخدمين
    /// User management service interface
    /// </summary>
    public interface IUserService
    {
        /// <summary>
        /// الحصول على جميع المستخدمين
        /// </summary>
        Task<IEnumerable<User>> GetAllUsersAsync();

        /// <summary>
        /// الحصول على مستخدم بواسطة المعرف
        /// </summary>
        Task<User?> GetUserByIdAsync(int userId);

        /// <summary>
        /// الحصول على مستخدم بواسطة اسم المستخدم
        /// </summary>
        Task<User?> GetUserByUsernameAsync(string username);

        /// <summary>
        /// الحصول على مستخدم بواسطة البريد الإلكتروني
        /// </summary>
        Task<User?> GetUserByEmailAsync(string email);

        /// <summary>
        /// الحصول على المستخدمين بواسطة الدور
        /// </summary>
        Task<IEnumerable<User>> GetUsersByRoleAsync(UserRole role);

        /// <summary>
        /// الحصول على المستخدمين بواسطة الحالة
        /// </summary>
        Task<IEnumerable<User>> GetUsersByStatusAsync(UserStatus status);

        /// <summary>
        /// البحث عن المستخدمين بواسطة الاسم
        /// </summary>
        Task<IEnumerable<User>> SearchUsersByNameAsync(string name);

        /// <summary>
        /// إضافة مستخدم جديد
        /// </summary>
        Task<int> AddUserAsync(User user, string password);

        /// <summary>
        /// تحديث بيانات مستخدم
        /// </summary>
        Task<bool> UpdateUserAsync(User user);

        /// <summary>
        /// حذف مستخدم
        /// </summary>
        Task<bool> DeleteUserAsync(int userId);

        /// <summary>
        /// تغيير حالة المستخدم
        /// </summary>
        Task<bool> ChangeUserStatusAsync(int userId, UserStatus newStatus, int modifiedByUserId);

        /// <summary>
        /// تغيير كلمة مرور المستخدم
        /// </summary>
        Task<bool> ChangePasswordAsync(int userId, string newPassword, int modifiedByUserId);

        /// <summary>
        /// إعادة تعيين كلمة المرور
        /// </summary>
        Task<string> ResetPasswordAsync(int userId, int modifiedByUserId);

        /// <summary>
        /// قفل حساب المستخدم
        /// </summary>
        Task<bool> LockUserAccountAsync(int userId, DateTime lockUntil, int modifiedByUserId);

        /// <summary>
        /// إلغاء قفل حساب المستخدم
        /// </summary>
        Task<bool> UnlockUserAccountAsync(int userId, int modifiedByUserId);

        /// <summary>
        /// تسجيل محاولة دخول فاشلة
        /// </summary>
        Task<bool> RecordFailedLoginAttemptAsync(int userId);

        /// <summary>
        /// إعادة تعيين محاولات الدخول الفاشلة
        /// </summary>
        Task<bool> ResetFailedLoginAttemptsAsync(int userId);

        /// <summary>
        /// تحديث تاريخ آخر دخول
        /// </summary>
        Task<bool> UpdateLastLoginDateAsync(int userId);

        /// <summary>
        /// التحقق من وجود اسم مستخدم
        /// </summary>
        Task<bool> IsUsernameExistsAsync(string username, int? excludeUserId = null);

        /// <summary>
        /// التحقق من وجود بريد إلكتروني
        /// </summary>
        Task<bool> IsEmailExistsAsync(string email, int? excludeUserId = null);

        /// <summary>
        /// الحصول على صلاحيات المستخدم
        /// </summary>
        Task<UserPermissions> GetUserPermissionsAsync(int userId);

        /// <summary>
        /// تحديث صلاحيات المستخدم
        /// </summary>
        Task<bool> UpdateUserPermissionsAsync(int userId, UserPermissions permissions, int modifiedByUserId);

        /// <summary>
        /// الحصول على إحصائيات المستخدمين
        /// </summary>
        Task<UserStatistics> GetUserStatisticsAsync();

        /// <summary>
        /// الحصول على المستخدمين النشطين
        /// </summary>
        Task<IEnumerable<User>> GetActiveUsersAsync();

        /// <summary>
        /// الحصول على المستخدمين الذين لم يدخلوا منذ فترة معينة
        /// </summary>
        Task<IEnumerable<User>> GetInactiveUsersAsync(int daysSinceLastLogin);
    }

    /// <summary>
    /// إحصائيات المستخدمين
    /// </summary>
    public class UserStatistics
    {
        public int TotalUsers { get; set; }
        public int ActiveUsers { get; set; }
        public int InactiveUsers { get; set; }
        public int SuspendedUsers { get; set; }
        public int LockedUsers { get; set; }
        public int BannedUsers { get; set; }
        public Dictionary<UserRole, int> UsersByRole { get; set; } = new();
        public int UsersLoggedInToday { get; set; }
        public int UsersLoggedInThisWeek { get; set; }
        public int UsersLoggedInThisMonth { get; set; }
    }
}
