using System;
using System.ComponentModel.DataAnnotations;

namespace AlNoorEducationalInstitute.Models.Institution
{
    /// <summary>
    /// نموذج ملف المؤسسة التعليمية
    /// Institution profile model
    /// </summary>
    public class InstitutionProfile
    {
        public int ProfileId { get; set; }
        
        [Required(ErrorMessage = "اسم المؤسسة مطلوب")]
        [StringLength(200, ErrorMessage = "اسم المؤسسة يجب أن يكون أقل من 200 حرف")]
        public string InstitutionName { get; set; } = "مؤسسة النور التربوي";
        
        [StringLength(200, ErrorMessage = "الاسم الإنجليزي يجب أن يكون أقل من 200 حرف")]
        public string InstitutionNameEn { get; set; } = "Al-Noor Educational Institute";
        
        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string Description { get; set; } = string.Empty;
        
        [StringLength(500, ErrorMessage = "الوصف الإنجليزي يجب أن يكون أقل من 500 حرف")]
        public string DescriptionEn { get; set; } = string.Empty;
        
        // معلومات الاتصال
        [StringLength(500, ErrorMessage = "العنوان يجب أن يكون أقل من 500 حرف")]
        public string Address { get; set; } = string.Empty;
        
        [StringLength(500, ErrorMessage = "العنوان الإنجليزي يجب أن يكون أقل من 500 حرف")]
        public string AddressEn { get; set; } = string.Empty;
        
        [StringLength(100, ErrorMessage = "المدينة يجب أن تكون أقل من 100 حرف")]
        public string City { get; set; } = string.Empty;
        
        [StringLength(100, ErrorMessage = "المنطقة يجب أن تكون أقل من 100 حرف")]
        public string Region { get; set; } = string.Empty;
        
        [StringLength(20, ErrorMessage = "الرمز البريدي يجب أن يكون أقل من 20 حرف")]
        public string PostalCode { get; set; } = string.Empty;
        
        [StringLength(100, ErrorMessage = "الدولة يجب أن تكون أقل من 100 حرف")]
        public string Country { get; set; } = "المملكة العربية السعودية";
        
        [Phone(ErrorMessage = "رقم الهاتف غير صحيح")]
        [StringLength(20, ErrorMessage = "رقم الهاتف يجب أن يكون أقل من 20 حرف")]
        public string PhoneNumber { get; set; } = string.Empty;
        
        [Phone(ErrorMessage = "رقم الفاكس غير صحيح")]
        [StringLength(20, ErrorMessage = "رقم الفاكس يجب أن يكون أقل من 20 حرف")]
        public string FaxNumber { get; set; } = string.Empty;
        
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [StringLength(100, ErrorMessage = "البريد الإلكتروني يجب أن يكون أقل من 100 حرف")]
        public string Email { get; set; } = string.Empty;
        
        [Url(ErrorMessage = "رابط الموقع الإلكتروني غير صحيح")]
        [StringLength(200, ErrorMessage = "رابط الموقع يجب أن يكون أقل من 200 حرف")]
        public string Website { get; set; } = string.Empty;
        
        // معلومات التراخيص والاعتمادات
        [StringLength(100, ErrorMessage = "رقم الترخيص يجب أن يكون أقل من 100 حرف")]
        public string LicenseNumber { get; set; } = string.Empty;
        
        public DateTime? LicenseIssueDate { get; set; }
        public DateTime? LicenseExpiryDate { get; set; }
        
        [StringLength(200, ErrorMessage = "جهة الإصدار يجب أن تكون أقل من 200 حرف")]
        public string LicenseIssuedBy { get; set; } = string.Empty;
        
        [StringLength(100, ErrorMessage = "رقم السجل التجاري يجب أن يكون أقل من 100 حرف")]
        public string CommercialRegistrationNumber { get; set; } = string.Empty;
        
        [StringLength(100, ErrorMessage = "الرقم الضريبي يجب أن يكون أقل من 100 حرف")]
        public string TaxNumber { get; set; } = string.Empty;
        
        // الشعار والهوية البصرية
        public byte[]? LogoImage { get; set; }
        
        [StringLength(50, ErrorMessage = "نوع ملف الشعار يجب أن يكون أقل من 50 حرف")]
        public string LogoFileType { get; set; } = string.Empty;
        
        [StringLength(200, ErrorMessage = "اسم ملف الشعار يجب أن يكون أقل من 200 حرف")]
        public string LogoFileName { get; set; } = string.Empty;
        
        public long LogoFileSize { get; set; }
        public DateTime? LogoUploadDate { get; set; }
        public int? LogoUploadedByUserId { get; set; }
        
        // الألوان الأساسية للمؤسسة
        [StringLength(7, ErrorMessage = "اللون الأساسي يجب أن يكون بصيغة hex")]
        public string PrimaryColor { get; set; } = "#2E8B57"; // Sea Green
        
        [StringLength(7, ErrorMessage = "اللون الثانوي يجب أن يكون بصيغة hex")]
        public string SecondaryColor { get; set; } = "#FFD700"; // Gold
        
        [StringLength(7, ErrorMessage = "لون النص يجب أن يكون بصيغة hex")]
        public string TextColor { get; set; } = "#2F4F4F"; // Dark Slate Gray
        
        // إعدادات العرض
        public bool ShowLogoInReports { get; set; } = true;
        public bool ShowLogoInInvoices { get; set; } = true;
        public bool ShowLogoInCertificates { get; set; } = true;
        public bool ShowLogoInLetters { get; set; } = true;
        public bool ShowLogoInLoginScreen { get; set; } = true;
        public bool ShowLogoInDashboard { get; set; } = true;
        
        // معلومات إضافية
        public InstitutionType Type { get; set; } = InstitutionType.Private;
        public EducationLevel[] SupportedLevels { get; set; } = Array.Empty<EducationLevel>();
        public string[] SupportedLanguages { get; set; } = new[] { "ar", "en" };
        
        public int EstablishedYear { get; set; }
        public int StudentCapacity { get; set; }
        public int CurrentStudentCount { get; set; }
        public int EmployeeCount { get; set; }
        
        // معلومات النظام
        public DateTime CreatedDate { get; set; }
        public DateTime LastModifiedDate { get; set; }
        public int LastModifiedByUserId { get; set; }
        public string LastModifiedByUserName { get; set; } = string.Empty;
        
        // خصائص محسوبة
        public bool HasLogo => LogoImage != null && LogoImage.Length > 0;
        public string LogoSizeFormatted => LogoFileSize > 0 ? FormatFileSize(LogoFileSize) : "0 KB";
        public bool IsLicenseValid => LicenseExpiryDate.HasValue && LicenseExpiryDate.Value > DateTime.Now;
        public int DaysUntilLicenseExpiry => LicenseExpiryDate.HasValue ? 
            Math.Max(0, (int)(LicenseExpiryDate.Value - DateTime.Now).TotalDays) : 0;
        
        private static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }

    /// <summary>
    /// نموذج إعدادات الشعار
    /// Logo settings model
    /// </summary>
    public class LogoSettings
    {
        public int MaxFileSizeBytes { get; set; } = 2 * 1024 * 1024; // 2 MB
        public string[] AllowedFileTypes { get; set; } = { "image/png", "image/jpeg", "image/jpg", "image/gif" };
        public string[] AllowedFileExtensions { get; set; } = { ".png", ".jpg", ".jpeg", ".gif" };
        public int MaxWidthPixels { get; set; } = 1024;
        public int MaxHeightPixels { get; set; } = 1024;
        public int MinWidthPixels { get; set; } = 100;
        public int MinHeightPixels { get; set; } = 100;
        public bool AutoResize { get; set; } = true;
        public int DefaultWidthPixels { get; set; } = 300;
        public int DefaultHeightPixels { get; set; } = 200;
        public bool MaintainAspectRatio { get; set; } = true;
        public int JpegQuality { get; set; } = 85; // 0-100
    }

    /// <summary>
    /// نموذج معلومات الشعار
    /// Logo information model
    /// </summary>
    public class LogoInfo
    {
        public bool HasLogo { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string FileType { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public string FileSizeFormatted { get; set; } = string.Empty;
        public DateTime? UploadDate { get; set; }
        public string UploadedBy { get; set; } = string.Empty;
        public int Width { get; set; }
        public int Height { get; set; }
        public string Dimensions => $"{Width} × {Height}";
        public byte[]? ImageData { get; set; }
    }

    /// <summary>
    /// نموذج نتيجة رفع الشعار
    /// Logo upload result model
    /// </summary>
    public class LogoUploadResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string ErrorCode { get; set; } = string.Empty;
        public LogoInfo? LogoInfo { get; set; }
        public List<string> ValidationErrors { get; set; } = new();
    }

    /// <summary>
    /// تعداد أنواع المؤسسات التعليمية
    /// Educational institution types enumeration
    /// </summary>
    public enum InstitutionType
    {
        Public = 1,      // حكومي
        Private = 2,     // أهلي
        International = 3, // دولي
        Religious = 4,   // ديني
        Technical = 5,   // تقني
        Special = 6      // ذوي الاحتياجات الخاصة
    }

    /// <summary>
    /// تعداد أحجام الشعار للاستخدامات المختلفة
    /// Logo sizes for different uses enumeration
    /// </summary>
    public enum LogoSize
    {
        Small = 1,    // 64x64 - للأيقونات
        Medium = 2,   // 128x128 - للواجهات
        Large = 3,    // 256x256 - للتقارير
        XLarge = 4,   // 512x512 - للطباعة
        Original = 5  // الحجم الأصلي
    }

    /// <summary>
    /// تعداد مواضع عرض الشعار
    /// Logo display positions enumeration
    /// </summary>
    public enum LogoPosition
    {
        TopLeft = 1,
        TopCenter = 2,
        TopRight = 3,
        MiddleLeft = 4,
        MiddleCenter = 5,
        MiddleRight = 6,
        BottomLeft = 7,
        BottomCenter = 8,
        BottomRight = 9
    }
}
