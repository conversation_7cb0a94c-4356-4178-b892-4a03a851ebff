@echo off
chcp 65001 > nul
echo ========================================
echo    Al-Noor Educational Institute Setup
echo ========================================
echo.

echo Checking system requirements...
echo.

REM Check if .NET is installed
dotnet --version > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo .NET is not installed
    echo.
    echo Please download and install .NET from:
    echo https://dotnet.microsoft.com/download
    echo.
    pause
    exit /b 1
)

echo .NET is installed
echo.

echo Creating required directories...

if not exist "AlNoorEducationalInstitute\Database" mkdir "AlNoorEducationalInstitute\Database"
if not exist "AlNoorEducationalInstitute\Database\Backup" mkdir "AlNoorEducationalInstitute\Database\Backup"
if not exist "AlNoorEducationalInstitute\Logs" mkdir "AlNoorEducationalInstitute\Logs"

echo Directories created successfully
echo.

echo Restoring NuGet packages...

dotnet restore

if %ERRORLEVEL% NEQ 0 (
    echo Package restore failed
    pause
    exit /b 1
)

echo Packages restored successfully
echo.

echo Building project...

dotnet build --configuration Release

if %ERRORLEVEL% NEQ 0 (
    echo Build failed
    pause
    exit /b 1
)

echo Project built successfully
echo.

echo ========================================
echo         Setup completed successfully!
echo ========================================
echo.
echo You can now run the application using:
echo.
echo   run.bat
echo   or
echo   run.ps1
echo.
echo Default login credentials:
echo   Username: admin
echo   Password: admin123
echo.
echo It's recommended to change the password after first login
echo.
pause
