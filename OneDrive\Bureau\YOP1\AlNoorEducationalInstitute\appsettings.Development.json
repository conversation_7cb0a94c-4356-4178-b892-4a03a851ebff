{"ConnectionStrings": {"DefaultConnection": "Data Source=Database\\AlNoorDB_Dev.db;Version=3;", "BackupConnection": "Data Source=Database\\Backup\\AlNoorDB_Dev_Backup.db;Version=3;"}, "ApplicationSettings": {"InstituteName": "مؤسسة النور التربوي - بيئة التطوير", "InstituteNameEnglish": "Al-Noor Educational Institute - Development", "Version": "1.0.0-dev", "DatabaseVersion": "1.0-dev", "MaxLoginAttempts": 10, "SessionTimeoutMinutes": 120, "BackupIntervalHours": 1, "LogLevel": "Debug"}, "Security": {"PasswordMinLength": 4, "RequireSpecialCharacters": false, "RequireNumbers": false, "RequireUppercase": false, "RequireLowercase": false, "PasswordExpirationDays": 365, "AccountLockoutDuration": 1}, "UI": {"DefaultLanguage": "ar", "SupportedLanguages": ["ar", "en"], "Theme": "Development", "FontFamily": "<PERSON><PERSON><PERSON>", "FontSize": 12, "ShowDebugInfo": true}, "Logging": {"LogLevel": {"Default": "Debug", "Microsoft": "Information", "Microsoft.Hosting.Lifetime": "Information", "AlNoorEducationalInstitute": "Debug"}, "Console": {"IncludeScopes": true, "TimestampFormat": "yyyy-MM-dd HH:mm:ss.fff "}, "File": {"Path": "Logs\\AlNoor_Dev_{Date}.log", "MaxFileSizeMB": 50, "MaxFiles": 10}}, "Development": {"EnableDetailedErrors": true, "ShowSensitiveDataInLogs": false, "EnableDatabaseSeeding": true, "CreateTestData": true, "SkipDatabaseMigrations": false}}