using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AlNoorEducationalInstitute.Models;

namespace AlNoorEducationalInstitute.Services
{
    /// <summary>
    /// واجهة خدمة إدارة الطلاب
    /// Student management service interface
    /// </summary>
    public interface IStudentService
    {
        /// <summary>
        /// الحصول على جميع الطلاب
        /// </summary>
        Task<IEnumerable<Student>> GetAllStudentsAsync();

        /// <summary>
        /// الحصول على طالب بواسطة المعرف
        /// </summary>
        Task<Student?> GetStudentByIdAsync(int studentId);

        /// <summary>
        /// الحصول على طالب بواسطة الرقم التعريفي
        /// </summary>
        Task<Student?> GetStudentByNumberAsync(string studentNumber);

        /// <summary>
        /// البحث عن الطلاب بواسطة الاسم
        /// </summary>
        Task<IEnumerable<Student>> SearchStudentsByNameAsync(string name);

        /// <summary>
        /// الحصول على طلاب فصل معين
        /// </summary>
        Task<IEnumerable<Student>> GetStudentsByClassAsync(int classId);

        /// <summary>
        /// الحصول على الطلاب بواسطة الحالة
        /// </summary>
        Task<IEnumerable<Student>> GetStudentsByStatusAsync(StudentStatus status);

        /// <summary>
        /// إضافة طالب جديد
        /// </summary>
        Task<int> AddStudentAsync(Student student);

        /// <summary>
        /// تحديث بيانات طالب
        /// </summary>
        Task<bool> UpdateStudentAsync(Student student);

        /// <summary>
        /// حذف طالب
        /// </summary>
        Task<bool> DeleteStudentAsync(int studentId);

        /// <summary>
        /// أرشفة طالب (تغيير حالته)
        /// </summary>
        Task<bool> ArchiveStudentAsync(int studentId, StudentStatus newStatus, int userId);

        /// <summary>
        /// نقل طالب إلى فصل آخر
        /// </summary>
        Task<bool> TransferStudentToClassAsync(int studentId, int newClassId, int userId);

        /// <summary>
        /// التحقق من وجود رقم تعريفي للطالب
        /// </summary>
        Task<bool> IsStudentNumberExistsAsync(string studentNumber, int? excludeStudentId = null);

        /// <summary>
        /// الحصول على إحصائيات الطلاب
        /// </summary>
        Task<StudentStatistics> GetStudentStatisticsAsync();

        /// <summary>
        /// الحصول على الطلاب المسجلين في فترة زمنية معينة
        /// </summary>
        Task<IEnumerable<Student>> GetStudentsEnrolledBetweenAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// تصدير بيانات الطلاب
        /// </summary>
        Task<byte[]> ExportStudentsToExcelAsync(IEnumerable<int>? studentIds = null);

        /// <summary>
        /// استيراد بيانات الطلاب من ملف Excel
        /// </summary>
        Task<ImportResult> ImportStudentsFromExcelAsync(byte[] fileData, int userId);
    }

    /// <summary>
    /// إحصائيات الطلاب
    /// </summary>
    public class StudentStatistics
    {
        public int TotalStudents { get; set; }
        public int ActiveStudents { get; set; }
        public int GraduatedStudents { get; set; }
        public int DroppedStudents { get; set; }
        public int TransferredStudents { get; set; }
        public int MaleStudents { get; set; }
        public int FemaleStudents { get; set; }
        public Dictionary<int, int> StudentsByClass { get; set; } = new();
        public Dictionary<EducationLevel, int> StudentsByLevel { get; set; } = new();
    }

    /// <summary>
    /// نتيجة عملية الاستيراد
    /// </summary>
    public class ImportResult
    {
        public bool Success { get; set; }
        public int TotalRecords { get; set; }
        public int SuccessfulImports { get; set; }
        public int FailedImports { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
    }
}
