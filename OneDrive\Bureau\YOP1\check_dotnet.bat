@echo off
chcp 65001 > nul
echo ========================================
echo    .NET Installation Check
echo ========================================
echo.

echo Checking for .NET Runtime...
dotnet --list-runtimes 2>nul
if %ERRORLEVEL% EQU 0 (
    echo.
    echo .NET Runtime is available
    echo.
    echo Checking for .NET SDK...
    dotnet --list-sdks 2>nul
    if %ERRORLEVEL% EQU 0 (
        echo .NET SDK is available
        echo.
        echo You can build and run the project using:
        echo   setup.bat
        echo   run.bat
    ) else (
        echo .NET SDK is NOT available
        echo You can only run pre-built applications
        echo.
        echo To build the project, please install .NET SDK from:
        echo https://dotnet.microsoft.com/download
    )
) else (
    echo .NET is NOT installed
    echo.
    echo Please install .NET from:
    echo https://dotnet.microsoft.com/download
    echo.
    echo For this application, you need .NET 6.0 or later
)

echo.
echo ========================================
pause
