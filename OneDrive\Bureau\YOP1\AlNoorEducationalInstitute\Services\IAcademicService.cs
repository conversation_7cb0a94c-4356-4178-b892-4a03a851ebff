using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AlNoorEducationalInstitute.Models;

namespace AlNoorEducationalInstitute.Services
{
    /// <summary>
    /// واجهة خدمة النظام الأكاديمي
    /// Academic service interface
    /// </summary>
    public interface IAcademicService
    {
        // إدارة الدرجات
        Task<IEnumerable<Grade>> GetAllGradesAsync();
        Task<Grade?> GetGradeByIdAsync(int gradeId);
        Task<IEnumerable<Grade>> GetGradesByStudentAsync(int studentId);
        Task<IEnumerable<Grade>> GetGradesBySubjectAsync(int subjectId);
        Task<IEnumerable<Grade>> GetGradesByClassAsync(int classId);
        Task<IEnumerable<Grade>> GetGradesByStudentAndSemesterAsync(int studentId, Semester semester, string academicYear);
        Task<int> AddGradeAsync(Grade grade);
        Task<bool> UpdateGradeAsync(Grade grade);
        Task<bool> DeleteGradeAsync(int gradeId);
        Task<bool> ApproveGradeAsync(int gradeId, int approvedByUserId);
        Task<bool> ApproveGradesAsync(IEnumerable<int> gradeIds, int approvedByUserId);

        // إدارة الحضور والغياب
        Task<IEnumerable<Attendance>> GetAllAttendanceAsync();
        Task<Attendance?> GetAttendanceByIdAsync(int attendanceId);
        Task<IEnumerable<Attendance>> GetAttendanceByStudentAsync(int studentId);
        Task<IEnumerable<Attendance>> GetAttendanceByClassAsync(int classId, DateTime date);
        Task<IEnumerable<Attendance>> GetAttendanceByDateRangeAsync(int studentId, DateTime startDate, DateTime endDate);
        Task<int> AddAttendanceAsync(Attendance attendance);
        Task<bool> UpdateAttendanceAsync(Attendance attendance);
        Task<bool> DeleteAttendanceAsync(int attendanceId);
        Task<bool> MarkClassAttendanceAsync(int classId, DateTime date, Dictionary<int, AttendanceStatus> studentAttendance, int userId);

        // إدارة التقارير الأكاديمية
        Task<IEnumerable<StudentReport>> GetAllStudentReportsAsync();
        Task<StudentReport?> GetStudentReportByIdAsync(int reportId);
        Task<StudentReport?> GetStudentReportAsync(int studentId, Semester semester, string academicYear);
        Task<IEnumerable<StudentReport>> GetStudentReportsByClassAsync(int classId, Semester semester, string academicYear);
        Task<int> AddStudentReportAsync(StudentReport report);
        Task<bool> UpdateStudentReportAsync(StudentReport report);
        Task<bool> DeleteStudentReportAsync(int reportId);
        Task<bool> ApproveStudentReportAsync(int reportId, int approvedByUserId);

        // العمليات الأكاديمية
        Task<StudentReport> GenerateStudentReportAsync(int studentId, Semester semester, string academicYear, int userId);
        Task<IEnumerable<StudentReport>> GenerateClassReportsAsync(int classId, Semester semester, string academicYear, int userId);
        Task<decimal> CalculateStudentAverageAsync(int studentId, Semester semester, string academicYear);
        Task<decimal> CalculateSubjectAverageAsync(int studentId, int subjectId, Semester semester, string academicYear);
        Task<AttendanceStatistics> CalculateAttendanceStatisticsAsync(int studentId, Semester semester, string academicYear);
        Task<ClassStatistics> CalculateClassStatisticsAsync(int classId, Semester semester, string academicYear);

        // التقديرات والترتيب
        Task<string> GetGradeLetterAsync(decimal percentage);
        Task<int> CalculateClassRankAsync(int studentId, int classId, Semester semester, string academicYear);
        Task<IEnumerable<StudentRanking>> GetClassRankingAsync(int classId, Semester semester, string academicYear);
        Task<bool> IsStudentPassedAsync(int studentId, Semester semester, string academicYear);

        // التقارير الإحصائية
        Task<AcademicStatistics> GetAcademicStatisticsAsync(Semester semester, string academicYear);
        Task<SubjectStatistics> GetSubjectStatisticsAsync(int subjectId, Semester semester, string academicYear);
        Task<IEnumerable<StudentPerformance>> GetTopStudentsAsync(int classId, Semester semester, string academicYear, int count = 10);
        Task<IEnumerable<StudentPerformance>> GetLowPerformingStudentsAsync(int classId, Semester semester, string academicYear);

        // تصدير البيانات
        Task<byte[]> ExportGradesToExcelAsync(int classId, Semester semester, string academicYear);
        Task<byte[]> ExportAttendanceToExcelAsync(int classId, DateTime startDate, DateTime endDate);
        Task<byte[]> ExportStudentReportToExcelAsync(int studentId, Semester semester, string academicYear);
        Task<byte[]> ExportClassReportsToExcelAsync(int classId, Semester semester, string academicYear);
    }

    /// <summary>
    /// إحصائيات الحضور
    /// </summary>
    public class AttendanceStatistics
    {
        public int TotalDays { get; set; }
        public int PresentDays { get; set; }
        public int AbsentDays { get; set; }
        public int ExcusedAbsentDays { get; set; }
        public int LateDays { get; set; }
        public decimal AttendancePercentage { get; set; }
    }

    /// <summary>
    /// إحصائيات الفصل
    /// </summary>
    public class ClassStatistics
    {
        public int TotalStudents { get; set; }
        public int PassedStudents { get; set; }
        public int FailedStudents { get; set; }
        public decimal ClassAverage { get; set; }
        public decimal HighestAverage { get; set; }
        public decimal LowestAverage { get; set; }
        public decimal PassingPercentage { get; set; }
        public decimal AverageAttendance { get; set; }
    }

    /// <summary>
    /// ترتيب الطلاب
    /// </summary>
    public class StudentRanking
    {
        public int StudentId { get; set; }
        public string StudentName { get; set; } = string.Empty;
        public string StudentNumber { get; set; } = string.Empty;
        public decimal Average { get; set; }
        public int Rank { get; set; }
        public decimal AttendancePercentage { get; set; }
    }

    /// <summary>
    /// الإحصائيات الأكاديمية العامة
    /// </summary>
    public class AcademicStatistics
    {
        public int TotalStudents { get; set; }
        public int TotalClasses { get; set; }
        public int TotalSubjects { get; set; }
        public decimal OverallAverage { get; set; }
        public decimal OverallPassingRate { get; set; }
        public decimal OverallAttendanceRate { get; set; }
        public Dictionary<EducationLevel, decimal> AverageByLevel { get; set; } = new();
        public Dictionary<int, decimal> AverageByGrade { get; set; } = new();
    }

    /// <summary>
    /// إحصائيات المادة
    /// </summary>
    public class SubjectStatistics
    {
        public int SubjectId { get; set; }
        public string SubjectName { get; set; } = string.Empty;
        public int TotalStudents { get; set; }
        public int PassedStudents { get; set; }
        public int FailedStudents { get; set; }
        public decimal SubjectAverage { get; set; }
        public decimal HighestGrade { get; set; }
        public decimal LowestGrade { get; set; }
        public decimal PassingRate { get; set; }
    }

    /// <summary>
    /// أداء الطالب
    /// </summary>
    public class StudentPerformance
    {
        public int StudentId { get; set; }
        public string StudentName { get; set; } = string.Empty;
        public string StudentNumber { get; set; } = string.Empty;
        public decimal Average { get; set; }
        public int Rank { get; set; }
        public decimal AttendancePercentage { get; set; }
        public int PassedSubjects { get; set; }
        public int FailedSubjects { get; set; }
        public string OverallGrade { get; set; } = string.Empty;
    }
}
