{"ConnectionStrings": {"DefaultConnection": "Data Source=Database\\AlNoorDB.db;Version=3;", "BackupConnection": "Data Source=Database\\Backup\\AlNoorDB_Backup.db;Version=3;"}, "ApplicationSettings": {"InstituteName": "مؤسسة النور التربوي", "InstituteNameEnglish": "Al-Noor Educational Institute", "Version": "1.0.0", "DatabaseVersion": "1.0", "MaxLoginAttempts": 3, "SessionTimeoutMinutes": 30, "BackupIntervalHours": 24, "LogLevel": "Information"}, "Security": {"PasswordMinLength": 8, "RequireSpecialCharacters": true, "RequireNumbers": true, "RequireUppercase": true, "RequireLowercase": true, "PasswordExpirationDays": 90, "AccountLockoutDuration": 15}, "UI": {"DefaultLanguage": "ar", "SupportedLanguages": ["ar", "en"], "Theme": "<PERSON><PERSON><PERSON>", "FontFamily": "<PERSON><PERSON><PERSON>", "FontSize": 12, "ShowDebugInfo": false}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "AlNoorEducationalInstitute": "Information"}, "Console": {"IncludeScopes": false, "TimestampFormat": "yyyy-MM-dd HH:mm:ss "}, "File": {"Path": "Logs\\AlNoor_{Date}.log", "MaxFileSizeMB": 10, "MaxFiles": 30}}, "Production": {"EnableDetailedErrors": false, "ShowSensitiveDataInLogs": false, "EnableDatabaseSeeding": false, "CreateTestData": false, "SkipDatabaseMigrations": false}}