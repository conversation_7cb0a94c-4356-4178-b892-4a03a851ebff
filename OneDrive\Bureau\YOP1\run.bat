@echo off
chcp 65001 > nul
echo ========================================
echo    نظام إدارة مؤسسة النور التربوي
echo    Al-Noor Educational Institute
echo ========================================
echo.

echo جاري بناء المشروع...
echo Building project...
dotnet build --configuration Release

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ فشل في بناء المشروع
    echo ❌ Build failed
    pause
    exit /b 1
)

echo.
echo ✅ تم بناء المشروع بنجاح
echo ✅ Build successful
echo.

echo جاري تشغيل التطبيق...
echo Starting application...
echo.

cd AlNoorEducationalInstitute
dotnet run

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ فشل في تشغيل التطبيق
    echo ❌ Failed to start application
    pause
    exit /b 1
)

echo.
echo تم إغلاق التطبيق
echo Application closed
pause
