using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AlNoorEducationalInstitute.Models.Backup;

namespace AlNoorEducationalInstitute.Services
{
    /// <summary>
    /// واجهة خدمة النسخ الاحتياطي المتقدمة
    /// Advanced backup service interface
    /// </summary>
    public interface IBackupService
    {
        // إنشاء النسخ الاحتياطية
        Task<int> CreateBackupAsync(BackupInfo backup);
        Task<int> CreateFullBackupAsync(string backupName, string description = "");
        Task<int> CreateIncrementalBackupAsync(string backupName, DateTime? lastBackupDate = null);
        Task<int> CreateDifferentialBackupAsync(string backupName, DateTime? baseBackupDate = null);
        Task<int> CreateCustomBackupAsync(string backupName, IEnumerable<string> tablesToInclude);

        // إدارة النسخ الاحتياطية
        Task<IEnumerable<BackupInfo>> GetAllBackupsAsync();
        Task<IEnumerable<BackupInfo>> GetBackupsByStatusAsync(BackupStatus status);
        Task<IEnumerable<BackupInfo>> GetBackupsByTypeAsync(BackupType type);
        Task<IEnumerable<BackupInfo>> GetBackupsByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<BackupInfo?> GetBackupByIdAsync(int backupId);
        Task<BackupInfo?> GetLatestBackupAsync(BackupType? type = null);

        // تنفيذ النسخ الاحتياطية
        Task<bool> ExecuteBackupAsync(int backupId);
        Task<bool> CancelBackupAsync(int backupId);
        Task<bool> RetryFailedBackupAsync(int backupId);
        Task<BackupStatus> GetBackupStatusAsync(int backupId);
        Task<decimal> GetBackupProgressAsync(int backupId);

        // حذف النسخ الاحتياطية
        Task<bool> DeleteBackupAsync(int backupId, bool deleteFiles = true);
        Task<int> DeleteExpiredBackupsAsync();
        Task<int> DeleteOldBackupsAsync(int daysToKeep);
        Task<bool> ArchiveOldBackupsAsync(DateTime cutoffDate, string archivePath);

        // جدولة النسخ الاحتياطية
        Task<int> CreateBackupScheduleAsync(BackupSchedule schedule);
        Task<bool> UpdateBackupScheduleAsync(BackupSchedule schedule);
        Task<bool> DeleteBackupScheduleAsync(int scheduleId);
        Task<IEnumerable<BackupSchedule>> GetAllSchedulesAsync();
        Task<IEnumerable<BackupSchedule>> GetActiveSchedulesAsync();
        Task<BackupSchedule?> GetScheduleByIdAsync(int scheduleId);
        Task<bool> EnableScheduleAsync(int scheduleId, bool enable);
        Task ProcessScheduledBackupsAsync();

        // استعادة النسخ الاحتياطية
        Task<int> CreateRestoreAsync(BackupRestore restore);
        Task<bool> ExecuteRestoreAsync(int restoreId);
        Task<bool> CancelRestoreAsync(int restoreId);
        Task<IEnumerable<BackupRestore>> GetAllRestoresAsync();
        Task<BackupRestore?> GetRestoreByIdAsync(int restoreId);
        Task<RestoreStatus> GetRestoreStatusAsync(int restoreId);
        Task<decimal> GetRestoreProgressAsync(int restoreId);

        // التحقق من صحة النسخ الاحتياطية
        Task<bool> VerifyBackupIntegrityAsync(int backupId);
        Task<bool> ValidateBackupFileAsync(string filePath);
        Task<object> GetBackupFileInfoAsync(string filePath);
        Task<bool> TestRestoreAsync(int backupId, string testDatabaseName);

        // إدارة الملفات
        Task<string> GetBackupFilePathAsync(int backupId);
        Task<long> GetBackupFileSizeAsync(int backupId);
        Task<string> CalculateBackupHashAsync(int backupId);
        Task<bool> CompressBackupAsync(int backupId);
        Task<bool> DecompressBackupAsync(int backupId);
        Task<bool> EncryptBackupAsync(int backupId, string encryptionKey);
        Task<bool> DecryptBackupAsync(int backupId, string encryptionKey);

        // النسخ الاحتياطي السحابي
        Task<bool> UploadToCloudAsync(int backupId, string cloudProvider, string accessKey, string secretKey, string bucketName);
        Task<bool> DownloadFromCloudAsync(int backupId, string cloudProvider, string accessKey, string secretKey, string bucketName);
        Task<IEnumerable<object>> ListCloudBackupsAsync(string cloudProvider, string accessKey, string secretKey, string bucketName);
        Task<bool> DeleteCloudBackupAsync(int backupId, string cloudProvider, string accessKey, string secretKey, string bucketName);

        // إعدادات النسخ الاحتياطي
        Task<BackupSettings> GetBackupSettingsAsync();
        Task<bool> UpdateBackupSettingsAsync(BackupSettings settings);
        Task<BackupSettings> GetDefaultBackupSettingsAsync();
        Task<bool> TestBackupLocationAsync(BackupLocation location, string path);

        // التقارير والإحصائيات
        Task<object> GetBackupStatisticsAsync(DateTime startDate, DateTime endDate);
        Task<object> GetBackupUsageReportAsync();
        Task<object> GetBackupPerformanceReportAsync(DateTime startDate, DateTime endDate);
        Task<IEnumerable<object>> GetBackupTrendsAsync(int months = 12);
        Task<object> GetStorageUsageReportAsync();

        // مراقبة النظام
        Task<object> GetSystemHealthAsync();
        Task<decimal> GetAvailableDiskSpaceAsync(string path);
        Task<object> GetBackupServiceStatusAsync();
        Task<IEnumerable<object>> GetActiveBackupJobsAsync();
        Task<object> GetBackupQueueStatusAsync();

        // التنبيهات والإشعارات
        Task SendBackupNotificationAsync(int backupId, bool isSuccess, string message = "");
        Task SendScheduleNotificationAsync(int scheduleId, string message);
        Task<bool> EnableBackupNotificationsAsync(bool enable);
        Task<IEnumerable<string>> GetNotificationRecipientsAsync();
        Task<bool> UpdateNotificationRecipientsAsync(IEnumerable<string> recipients);

        // إدارة قاعدة البيانات
        Task<IEnumerable<string>> GetDatabaseTablesAsync();
        Task<IEnumerable<BackupTable>> GetTableInfoAsync();
        Task<long> GetTableSizeAsync(string tableName);
        Task<int> GetTableRecordCountAsync(string tableName);
        Task<DateTime> GetTableLastModifiedAsync(string tableName);

        // الأمان والتشفير
        Task<string> GenerateEncryptionKeyAsync();
        Task<bool> ValidateEncryptionKeyAsync(string key);
        Task<string> HashPasswordAsync(string password);
        Task<bool> VerifyPasswordHashAsync(string password, string hash);

        // استيراد وتصدير الإعدادات
        Task<string> ExportBackupSettingsAsync();
        Task<bool> ImportBackupSettingsAsync(string settingsJson);
        Task<string> ExportSchedulesAsync();
        Task<bool> ImportSchedulesAsync(string schedulesJson);

        // الصيانة والتنظيف
        Task CleanupTempFilesAsync();
        Task CleanupOldLogsAsync(int daysToKeep = 30);
        Task OptimizeBackupStorageAsync();
        Task<bool> RepairCorruptedBackupAsync(int backupId);
        Task<object> RunSystemDiagnosticsAsync();

        // التكامل مع الخدمات الخارجية
        Task<bool> IntegrateWithCloudProviderAsync(string provider, Dictionary<string, string> credentials);
        Task<bool> TestCloudConnectionAsync(string provider, Dictionary<string, string> credentials);
        Task<IEnumerable<string>> GetSupportedCloudProvidersAsync();
        Task<object> GetCloudProviderSettingsAsync(string provider);

        // إدارة الأداء
        Task<TimeSpan> GetAverageBackupTimeAsync(BackupType type);
        Task<decimal> GetAverageBackupSizeAsync(BackupType type);
        Task<object> GetPerformanceMetricsAsync();
        Task<bool> OptimizeBackupPerformanceAsync();

        // التدقيق والسجلات
        Task<IEnumerable<object>> GetBackupAuditLogAsync(DateTime startDate, DateTime endDate);
        Task LogBackupOperationAsync(string operation, string details, bool isSuccess, string errorMessage = "");
        Task<object> GetBackupComplianceReportAsync();
        Task<bool> ValidateBackupComplianceAsync();

        // الاستعادة المتقدمة
        Task<bool> RestoreToPointInTimeAsync(DateTime targetDateTime, string targetDatabase);
        Task<bool> RestoreSelectiveTablesAsync(int backupId, IEnumerable<string> tablesToRestore, string targetDatabase);
        Task<bool> RestoreWithDataTransformationAsync(int backupId, Dictionary<string, object> transformationRules);
        Task<object> PreviewRestoreDataAsync(int backupId, string tableName, int maxRows = 100);

        // النسخ الاحتياطي التزايدي المتقدم
        Task<bool> CreateIncrementalChainAsync(string chainName, ScheduleFrequency frequency);
        Task<IEnumerable<object>> GetIncrementalChainsAsync();
        Task<bool> ValidateIncrementalChainAsync(string chainName);
        Task<bool> RepairIncrementalChainAsync(string chainName);

        // إدارة المساحة التخزينية
        Task<object> GetStorageQuotaAsync();
        Task<bool> SetStorageQuotaAsync(long maxSizeBytes);
        Task<object> GetStorageUsageByTypeAsync();
        Task<bool> CleanupStorageAsync(decimal targetFreeSpacePercentage);
    }
}
