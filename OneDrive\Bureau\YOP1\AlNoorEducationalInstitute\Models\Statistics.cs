using System;

namespace AlNoorEducationalInstitute.Models
{
    /// <summary>
    /// إحصائيات الفصول
    /// </summary>
    public class ClassStatistics
    {
        public int TotalClasses { get; set; }
        public int ActiveClasses { get; set; }
        public int ClosedClasses { get; set; }
        public int SuspendedClasses { get; set; }
        public int TotalStudents { get; set; }
        public double AverageStudentsPerClass { get; set; }
        public int ClassesWithMaxCapacity { get; set; }
        public int EmptyClasses { get; set; }
    }

    /// <summary>
    /// إحصائيات المواد
    /// </summary>
    public class SubjectStatistics
    {
        public int TotalSubjects { get; set; }
        public int ActiveSubjects { get; set; }
        public int SuspendedSubjects { get; set; }
        public int CancelledSubjects { get; set; }
        public int SubjectsWithTeachers { get; set; }
        public int SubjectsWithoutTeachers { get; set; }
        public double AverageSubjectsPerClass { get; set; }
        public int MostPopularSubjectId { get; set; }
        public string MostPopularSubjectName { get; set; } = string.Empty;
    }

    /// <summary>
    /// إحصائيات عامة للنظام
    /// </summary>
    public class SystemStatistics
    {
        public int TotalUsers { get; set; }
        public int ActiveUsers { get; set; }
        public int TotalStudents { get; set; }
        public int TotalEmployees { get; set; }
        public int TotalClasses { get; set; }
        public int TotalSubjects { get; set; }
        public DateTime LastBackupDate { get; set; }
        public long DatabaseSize { get; set; }
        public int TotalTransactions { get; set; }
        public decimal TotalRevenue { get; set; }
    }
}
