using System;
using System.Drawing;
using System.Windows.Forms;
using System.Threading.Tasks;
using System.Linq;
using LiveChartsCore;
using LiveChartsCore.SkiaSharpView;
using LiveChartsCore.SkiaSharpView.WinForms;
using AlNoorEducationalInstitute.Services;
using AlNoorEducationalInstitute.Models.Dashboard;

namespace AlNoorEducationalInstitute.Forms
{
    /// <summary>
    /// نافذة لوحة المعلومات التنفيذية
    /// Executive dashboard form
    /// </summary>
    public partial class ExecutiveDashboardForm : Form
    {
        private readonly IDashboardService _dashboardService;
        private readonly ILogger<ExecutiveDashboardForm> _logger;
        private Timer _refreshTimer;
        private DashboardKPIs _currentKPIs;

        // Main layout panels
        private TableLayoutPanel mainLayout;
        private Panel headerPanel;
        private Panel kpiPanel;
        private Panel chartsPanel;
        private Panel alertsPanel;

        // Header controls
        private Label titleLabel;
        private Label lastUpdateLabel;
        private Button refreshButton;
        private Button settingsButton;

        // K<PERSON> controls
        private Panel[] kpiCards;
        private Label[] kpiTitles;
        private Label[] kpiValues;
        private Label[] kpiTrends;

        // Chart controls
        private CartesianChart attendanceChart;
        private CartesianChart revenueChart;
        private PieChart distributionChart;
        private CartesianChart performanceChart;

        // Alerts controls
        private Panel alertsContainer;
        private Label alertsTitle;
        private FlowLayoutPanel alertsList;

        public ExecutiveDashboardForm(IDashboardService dashboardService, ILogger<ExecutiveDashboardForm> logger)
        {
            _dashboardService = dashboardService;
            _logger = logger;
            InitializeComponent();
            SetupForm();
            SetupRefreshTimer();
            LoadDashboardData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Text = "لوحة المعلومات التنفيذية - مؤسسة النور التربوي";
            this.Size = new Size(1400, 900);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.Font = new Font("Tahoma", 10F, FontStyle.Regular);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(248, 249, 250);

            // Main layout
            mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                RowCount = 4,
                ColumnCount = 1,
                BackColor = Color.Transparent
            };

            // Set row styles
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 80)); // Header
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 120)); // KPIs
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 70)); // Charts
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 30)); // Alerts

            this.Controls.Add(mainLayout);
            this.ResumeLayout(false);
        }

        private void SetupForm()
        {
            SetupHeaderPanel();
            SetupKPIPanel();
            SetupChartsPanel();
            SetupAlertsPanel();

            // Add panels to main layout
            mainLayout.Controls.Add(headerPanel, 0, 0);
            mainLayout.Controls.Add(kpiPanel, 0, 1);
            mainLayout.Controls.Add(chartsPanel, 0, 2);
            mainLayout.Controls.Add(alertsPanel, 0, 3);

            // Event handlers
            refreshButton.Click += RefreshButton_Click;
            settingsButton.Click += SettingsButton_Click;
        }

        private void SetupHeaderPanel()
        {
            headerPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(52, 73, 94),
                Padding = new Padding(20, 10, 20, 10)
            };

            // Title
            titleLabel = new Label
            {
                Text = "لوحة المعلومات التنفيذية",
                Font = new Font("Tahoma", 18F, FontStyle.Bold),
                ForeColor = Color.White,
                AutoSize = true,
                Location = new Point(20, 20)
            };

            // Last update
            lastUpdateLabel = new Label
            {
                Text = "آخر تحديث: جاري التحميل...",
                Font = new Font("Tahoma", 10F, FontStyle.Regular),
                ForeColor = Color.FromArgb(189, 195, 199),
                AutoSize = true,
                Location = new Point(20, 50)
            };

            // Refresh button
            refreshButton = new Button
            {
                Text = "تحديث",
                Size = new Size(100, 35),
                Location = new Point(headerPanel.Width - 230, 20),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
            refreshButton.FlatAppearance.BorderSize = 0;

            // Settings button
            settingsButton = new Button
            {
                Text = "إعدادات",
                Size = new Size(100, 35),
                Location = new Point(headerPanel.Width - 120, 20),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };
            settingsButton.FlatAppearance.BorderSize = 0;

            headerPanel.Controls.AddRange(new Control[]
            {
                titleLabel, lastUpdateLabel, refreshButton, settingsButton
            });

            // Handle resize to reposition buttons
            headerPanel.Resize += (s, e) =>
            {
                refreshButton.Location = new Point(headerPanel.Width - 230, 20);
                settingsButton.Location = new Point(headerPanel.Width - 120, 20);
            };
        }

        private void SetupKPIPanel()
        {
            kpiPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.Transparent,
                Padding = new Padding(20, 10, 20, 10)
            };

            // Create KPI cards
            var kpiCount = 6;
            kpiCards = new Panel[kpiCount];
            kpiTitles = new Label[kpiCount];
            kpiValues = new Label[kpiCount];
            kpiTrends = new Label[kpiCount];

            var cardWidth = 200;
            var cardHeight = 100;
            var spacing = 20;

            var kpiTitlesArray = new[]
            {
                "إجمالي الطلاب",
                "نسبة الحضور",
                "الإيرادات الشهرية",
                "المعدل الأكاديمي",
                "نسبة التحصيل",
                "الفواتير المتأخرة"
            };

            var kpiColors = new[]
            {
                Color.FromArgb(52, 152, 219),
                Color.FromArgb(46, 204, 113),
                Color.FromArgb(241, 196, 15),
                Color.FromArgb(155, 89, 182),
                Color.FromArgb(230, 126, 34),
                Color.FromArgb(231, 76, 60)
            };

            for (int i = 0; i < kpiCount; i++)
            {
                // Card panel
                kpiCards[i] = new Panel
                {
                    Size = new Size(cardWidth, cardHeight),
                    Location = new Point(20 + i * (cardWidth + spacing), 10),
                    BackColor = Color.White,
                    BorderStyle = BorderStyle.None
                };

                // Add shadow effect
                kpiCards[i].Paint += (s, e) =>
                {
                    var rect = new Rectangle(0, 0, cardWidth, cardHeight);
                    using (var brush = new SolidBrush(Color.FromArgb(30, 0, 0, 0)))
                    {
                        e.Graphics.FillRectangle(brush, rect.X + 2, rect.Y + 2, rect.Width, rect.Height);
                    }
                };

                // Color indicator
                var colorIndicator = new Panel
                {
                    Size = new Size(5, cardHeight),
                    Location = new Point(0, 0),
                    BackColor = kpiColors[i],
                    Dock = DockStyle.Left
                };

                // Title
                kpiTitles[i] = new Label
                {
                    Text = kpiTitlesArray[i],
                    Font = new Font("Tahoma", 10F, FontStyle.Regular),
                    ForeColor = Color.FromArgb(127, 140, 141),
                    Location = new Point(15, 15),
                    AutoSize = true
                };

                // Value
                kpiValues[i] = new Label
                {
                    Text = "جاري التحميل...",
                    Font = new Font("Tahoma", 16F, FontStyle.Bold),
                    ForeColor = Color.FromArgb(44, 62, 80),
                    Location = new Point(15, 40),
                    AutoSize = true
                };

                // Trend
                kpiTrends[i] = new Label
                {
                    Text = "",
                    Font = new Font("Tahoma", 9F, FontStyle.Regular),
                    ForeColor = Color.FromArgb(39, 174, 96),
                    Location = new Point(15, 75),
                    AutoSize = true
                };

                kpiCards[i].Controls.AddRange(new Control[]
                {
                    colorIndicator, kpiTitles[i], kpiValues[i], kpiTrends[i]
                });

                kpiPanel.Controls.Add(kpiCards[i]);
            }

            // Handle resize to reposition cards
            kpiPanel.Resize += (s, e) =>
            {
                var availableWidth = kpiPanel.Width - 40; // Subtract padding
                var totalSpacing = (kpiCount - 1) * spacing;
                var newCardWidth = (availableWidth - totalSpacing) / kpiCount;

                if (newCardWidth > 150) // Minimum card width
                {
                    for (int i = 0; i < kpiCount; i++)
                    {
                        kpiCards[i].Size = new Size((int)newCardWidth, cardHeight);
                        kpiCards[i].Location = new Point(20 + i * ((int)newCardWidth + spacing), 10);
                    }
                }
            };
        }

        private void SetupChartsPanel()
        {
            chartsPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.Transparent,
                Padding = new Padding(20, 10, 20, 10)
            };

            var chartsLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                RowCount = 2,
                ColumnCount = 2,
                BackColor = Color.Transparent
            };

            chartsLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 50));
            chartsLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 50));
            chartsLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50));
            chartsLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50));

            // Attendance chart
            var attendancePanel = CreateChartPanel("نسبة الحضور اليومي");
            attendanceChart = new CartesianChart
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White
            };
            attendancePanel.Controls.Add(attendanceChart);

            // Revenue chart
            var revenuePanel = CreateChartPanel("الإيرادات الشهرية");
            revenueChart = new CartesianChart
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White
            };
            revenuePanel.Controls.Add(revenueChart);

            // Distribution chart
            var distributionPanel = CreateChartPanel("توزيع الطلاب حسب المستوى");
            distributionChart = new PieChart
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White
            };
            distributionPanel.Controls.Add(distributionChart);

            // Performance chart
            var performancePanel = CreateChartPanel("مقارنة أداء الفصول");
            performanceChart = new CartesianChart
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White
            };
            performancePanel.Controls.Add(performanceChart);

            chartsLayout.Controls.Add(attendancePanel, 0, 0);
            chartsLayout.Controls.Add(revenuePanel, 1, 0);
            chartsLayout.Controls.Add(distributionPanel, 0, 1);
            chartsLayout.Controls.Add(performancePanel, 1, 1);

            chartsPanel.Controls.Add(chartsLayout);
        }

        private Panel CreateChartPanel(string title)
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Margin = new Padding(5),
                Padding = new Padding(10)
            };

            // Add shadow effect
            panel.Paint += (s, e) =>
            {
                var rect = panel.ClientRectangle;
                using (var brush = new SolidBrush(Color.FromArgb(20, 0, 0, 0)))
                {
                    e.Graphics.FillRectangle(brush, rect.X + 2, rect.Y + 2, rect.Width, rect.Height);
                }
            };

            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(44, 62, 80),
                Dock = DockStyle.Top,
                Height = 30,
                TextAlign = ContentAlignment.MiddleCenter
            };

            panel.Controls.Add(titleLabel);
            return panel;
        }

        private void SetupAlertsPanel()
        {
            alertsPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Margin = new Padding(20, 10, 20, 20),
                Padding = new Padding(15)
            };

            alertsTitle = new Label
            {
                Text = "التنبيهات والإشعارات",
                Font = new Font("Tahoma", 14F, FontStyle.Bold),
                ForeColor = Color.FromArgb(44, 62, 80),
                Dock = DockStyle.Top,
                Height = 40,
                TextAlign = ContentAlignment.MiddleRight
            };

            alertsList = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.TopDown,
                WrapContents = false,
                AutoScroll = true,
                BackColor = Color.Transparent
            };

            alertsPanel.Controls.AddRange(new Control[] { alertsTitle, alertsList });
        }

        private void SetupRefreshTimer()
        {
            _refreshTimer = new Timer
            {
                Interval = 300000 // 5 minutes
            };
            _refreshTimer.Tick += async (s, e) => await RefreshDashboardDataAsync();
            _refreshTimer.Start();
        }

        private async void LoadDashboardData()
        {
            try
            {
                await RefreshDashboardDataAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل بيانات لوحة المعلومات");
                MessageBox.Show("خطأ في تحميل بيانات لوحة المعلومات", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task RefreshDashboardDataAsync()
        {
            try
            {
                // Show loading state
                SetLoadingState(true);

                // Load KPIs
                _currentKPIs = await _dashboardService.GetDashboardKPIsAsync();
                UpdateKPICards();

                // Load charts data
                await LoadChartsDataAsync();

                // Load alerts
                await LoadAlertsAsync();

                // Update last update time
                lastUpdateLabel.Text = $"آخر تحديث: {DateTime.Now:yyyy/MM/dd HH:mm}";

                SetLoadingState(false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث بيانات لوحة المعلومات");
                SetLoadingState(false);
            }
        }

        private void UpdateKPICards()
        {
            if (_currentKPIs == null) return;

            // Update KPI values
            kpiValues[0].Text = _currentKPIs.TotalStudents.ToString();
            kpiValues[1].Text = $"{_currentKPIs.DailyAttendanceRate:F1}%";
            kpiValues[2].Text = $"{_currentKPIs.MonthlyRevenue:C0}";
            kpiValues[3].Text = $"{_currentKPIs.OverallAcademicAverage:F1}";
            kpiValues[4].Text = $"{_currentKPIs.CollectionRate:F1}%";
            kpiValues[5].Text = _currentKPIs.OverdueInvoices.ToString();

            // Update trends (mock data)
            kpiTrends[0].Text = "↗ ****%";
            kpiTrends[1].Text = "→ 0.1%";
            kpiTrends[2].Text = "↗ +12.5%";
            kpiTrends[3].Text = "↗ +2.3%";
            kpiTrends[4].Text = "↗ +3.1%";
            kpiTrends[5].Text = "↘ -15%";

            // Update trend colors
            for (int i = 0; i < kpiTrends.Length; i++)
            {
                if (kpiTrends[i].Text.StartsWith("↗"))
                    kpiTrends[i].ForeColor = Color.FromArgb(39, 174, 96);
                else if (kpiTrends[i].Text.StartsWith("↘"))
                    kpiTrends[i].ForeColor = Color.FromArgb(231, 76, 60);
                else
                    kpiTrends[i].ForeColor = Color.FromArgb(149, 165, 166);
            }
        }

        private async Task LoadChartsDataAsync()
        {
            try
            {
                // Load attendance chart data
                var attendanceData = await _dashboardService.GetDailyAttendanceAsync(
                    DateTime.Now.AddDays(-30), DateTime.Now);
                
                // Load revenue chart data
                var revenueData = await _dashboardService.GetMonthlyFinancialPerformanceAsync(DateTime.Now.Year);
                
                // Load distribution chart data
                var distributionData = await _dashboardService.GetStudentDistributionByLevelAsync();
                
                // Load performance chart data
                var performanceData = await _dashboardService.GetClassPerformanceComparisonAsync(
                    Semester.First, "2024-2025");

                // Update charts (basic implementation)
                UpdateAttendanceChart(attendanceData);
                UpdateRevenueChart(revenueData);
                UpdateDistributionChart(distributionData);
                UpdatePerformanceChart(performanceData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل بيانات الرسوم البيانية");
            }
        }

        private void UpdateAttendanceChart(IEnumerable<DailyAttendance> data)
        {
            // Basic chart update - will be enhanced with LiveCharts
            // For now, just show a placeholder
        }

        private void UpdateRevenueChart(IEnumerable<MonthlyFinancialPerformance> data)
        {
            // Basic chart update - will be enhanced with LiveCharts
        }

        private void UpdateDistributionChart(IEnumerable<StudentDistribution> data)
        {
            // Basic chart update - will be enhanced with LiveCharts
        }

        private void UpdatePerformanceChart(IEnumerable<ClassPerformanceComparison> data)
        {
            // Basic chart update - will be enhanced with LiveCharts
        }

        private async Task LoadAlertsAsync()
        {
            try
            {
                var alerts = await _dashboardService.GetActiveAlertsAsync(1); // Mock user ID
                
                alertsList.Controls.Clear();
                
                foreach (var alert in alerts.Take(5)) // Show only top 5 alerts
                {
                    var alertPanel = CreateAlertPanel(alert);
                    alertsList.Controls.Add(alertPanel);
                }

                if (!alerts.Any())
                {
                    var noAlertsLabel = new Label
                    {
                        Text = "لا توجد تنبيهات جديدة",
                        Font = new Font("Tahoma", 11F, FontStyle.Regular),
                        ForeColor = Color.FromArgb(149, 165, 166),
                        TextAlign = ContentAlignment.MiddleCenter,
                        Size = new Size(alertsList.Width - 20, 40)
                    };
                    alertsList.Controls.Add(noAlertsLabel);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل التنبيهات");
            }
        }

        private Panel CreateAlertPanel(DashboardAlert alert)
        {
            var panel = new Panel
            {
                Size = new Size(alertsList.Width - 20, 60),
                BackColor = GetAlertBackColor(alert.Type),
                Margin = new Padding(0, 5, 0, 5),
                Padding = new Padding(10)
            };

            var iconLabel = new Label
            {
                Text = GetAlertIcon(alert.Type),
                Font = new Font("Tahoma", 16F, FontStyle.Bold),
                ForeColor = GetAlertForeColor(alert.Type),
                Size = new Size(30, 40),
                Location = new Point(panel.Width - 40, 10),
                TextAlign = ContentAlignment.MiddleCenter
            };

            var titleLabel = new Label
            {
                Text = alert.Title,
                Font = new Font("Tahoma", 11F, FontStyle.Bold),
                ForeColor = GetAlertForeColor(alert.Type),
                Location = new Point(10, 10),
                Size = new Size(panel.Width - 60, 20),
                TextAlign = ContentAlignment.MiddleRight
            };

            var messageLabel = new Label
            {
                Text = alert.Message,
                Font = new Font("Tahoma", 9F, FontStyle.Regular),
                ForeColor = GetAlertForeColor(alert.Type),
                Location = new Point(10, 30),
                Size = new Size(panel.Width - 60, 20),
                TextAlign = ContentAlignment.MiddleRight
            };

            panel.Controls.AddRange(new Control[] { iconLabel, titleLabel, messageLabel });
            return panel;
        }

        private Color GetAlertBackColor(AlertType type)
        {
            return type switch
            {
                AlertType.Error => Color.FromArgb(255, 235, 238),
                AlertType.Warning => Color.FromArgb(255, 243, 205),
                AlertType.Success => Color.FromArgb(212, 237, 218),
                AlertType.Info => Color.FromArgb(209, 236, 241),
                _ => Color.FromArgb(248, 249, 250)
            };
        }

        private Color GetAlertForeColor(AlertType type)
        {
            return type switch
            {
                AlertType.Error => Color.FromArgb(114, 28, 36),
                AlertType.Warning => Color.FromArgb(133, 100, 4),
                AlertType.Success => Color.FromArgb(21, 87, 36),
                AlertType.Info => Color.FromArgb(12, 84, 96),
                _ => Color.FromArgb(73, 80, 87)
            };
        }

        private string GetAlertIcon(AlertType type)
        {
            return type switch
            {
                AlertType.Error => "⚠",
                AlertType.Warning => "⚡",
                AlertType.Success => "✓",
                AlertType.Info => "ℹ",
                _ => "•"
            };
        }

        private void SetLoadingState(bool isLoading)
        {
            refreshButton.Enabled = !isLoading;
            refreshButton.Text = isLoading ? "جاري التحديث..." : "تحديث";
        }

        private async void RefreshButton_Click(object sender, EventArgs e)
        {
            await RefreshDashboardDataAsync();
        }

        private void SettingsButton_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم فتح نافذة إعدادات لوحة المعلومات", "معلومات",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            _refreshTimer?.Stop();
            _refreshTimer?.Dispose();
            base.OnFormClosed(e);
        }
    }
}
