using System;
using System.ComponentModel.DataAnnotations;

namespace AlNoorEducationalInstitute.Models
{
    /// <summary>
    /// نموذج بيانات الدرجة
    /// Grade data model
    /// </summary>
    public class Grade
    {
        /// <summary>
        /// المعرف الفريد للدرجة
        /// </summary>
        public int GradeId { get; set; }

        /// <summary>
        /// معرف الطالب
        /// </summary>
        [Required(ErrorMessage = "معرف الطالب مطلوب")]
        public int StudentId { get; set; }

        /// <summary>
        /// معرف المادة الدراسية
        /// </summary>
        [Required(ErrorMessage = "معرف المادة مطلوب")]
        public int SubjectId { get; set; }

        /// <summary>
        /// معرف الفصل الدراسي
        /// </summary>
        [Required(ErrorMessage = "معرف الفصل مطلوب")]
        public int ClassId { get; set; }

        /// <summary>
        /// نوع التقييم (امتحان، فرض، مشاركة، إلخ)
        /// </summary>
        [Required(ErrorMessage = "نوع التقييم مطلوب")]
        public AssessmentType AssessmentType { get; set; }

        /// <summary>
        /// اسم التقييم أو الامتحان
        /// </summary>
        [Required(ErrorMessage = "اسم التقييم مطلوب")]
        [StringLength(100, ErrorMessage = "اسم التقييم يجب أن يكون أقل من 100 حرف")]
        public string AssessmentName { get; set; } = string.Empty;

        /// <summary>
        /// الدرجة المحصل عليها
        /// </summary>
        [Required(ErrorMessage = "الدرجة مطلوبة")]
        [Range(0, double.MaxValue, ErrorMessage = "الدرجة يجب أن تكون أكبر من أو تساوي الصفر")]
        public decimal Score { get; set; }

        /// <summary>
        /// الدرجة العظمى للتقييم
        /// </summary>
        [Required(ErrorMessage = "الدرجة العظمى مطلوبة")]
        [Range(1, double.MaxValue, ErrorMessage = "الدرجة العظمى يجب أن تكون أكبر من الصفر")]
        public decimal MaxScore { get; set; }

        /// <summary>
        /// النسبة المئوية
        /// </summary>
        public decimal Percentage => MaxScore > 0 ? (Score / MaxScore) * 100 : 0;

        /// <summary>
        /// وزن التقييم في المادة (من 100)
        /// </summary>
        [Range(0, 100, ErrorMessage = "وزن التقييم يجب أن يكون بين 0 و 100")]
        public decimal Weight { get; set; } = 100;

        /// <summary>
        /// الفصل الدراسي (الأول، الثاني، إلخ)
        /// </summary>
        [Required(ErrorMessage = "الفصل الدراسي مطلوب")]
        public Semester Semester { get; set; }

        /// <summary>
        /// العام الدراسي
        /// </summary>
        [Required(ErrorMessage = "العام الدراسي مطلوب")]
        [StringLength(20, ErrorMessage = "العام الدراسي يجب أن يكون أقل من 20 حرف")]
        public string AcademicYear { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ التقييم
        /// </summary>
        [Required(ErrorMessage = "تاريخ التقييم مطلوب")]
        public DateTime AssessmentDate { get; set; }

        /// <summary>
        /// هل تم اعتماد الدرجة؟
        /// </summary>
        public bool IsApproved { get; set; } = false;

        /// <summary>
        /// تاريخ الاعتماد
        /// </summary>
        public DateTime? ApprovalDate { get; set; }

        /// <summary>
        /// معرف المستخدم الذي اعتمد الدرجة
        /// </summary>
        public int? ApprovedByUserId { get; set; }

        /// <summary>
        /// ملاحظات على الدرجة
        /// </summary>
        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime LastModifiedDate { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أنشأ السجل
        /// </summary>
        public int CreatedByUserId { get; set; }

        /// <summary>
        /// معرف المستخدم الذي عدل السجل آخر مرة
        /// </summary>
        public int LastModifiedByUserId { get; set; }

        // Navigation Properties
        public virtual Student? Student { get; set; }
        public virtual Subject? Subject { get; set; }
        public virtual Class? Class { get; set; }
    }

    /// <summary>
    /// تعداد أنواع التقييم
    /// </summary>
    public enum AssessmentType
    {
        Quiz = 1,           // اختبار قصير
        Test = 2,           // اختبار
        MidtermExam = 3,    // امتحان نصف الفصل
        FinalExam = 4,      // امتحان نهائي
        Assignment = 5,     // واجب
        Project = 6,        // مشروع
        Participation = 7,  // مشاركة
        Homework = 8,       // واجب منزلي
        Presentation = 9,   // عرض تقديمي
        LabWork = 10,       // عمل مختبر
        Other = 99          // أخرى
    }

    /// <summary>
    /// تعداد الفصول الدراسية
    /// </summary>
    public enum Semester
    {
        First = 1,      // الفصل الأول
        Second = 2,     // الفصل الثاني
        Third = 3,      // الفصل الثالث (إن وجد)
        Summer = 4      // الفصل الصيفي
    }
}
