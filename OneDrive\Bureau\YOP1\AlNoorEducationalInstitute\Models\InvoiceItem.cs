using System;
using System.ComponentModel.DataAnnotations;

namespace AlNoorEducationalInstitute.Models
{
    /// <summary>
    /// نموذج بيانات عنصر الفاتورة
    /// Invoice item data model
    /// </summary>
    public class InvoiceItem
    {
        /// <summary>
        /// المعرف الفريد لعنصر الفاتورة
        /// </summary>
        public int InvoiceItemId { get; set; }

        /// <summary>
        /// معرف الفاتورة المرتبطة
        /// </summary>
        [Required(ErrorMessage = "معرف الفاتورة مطلوب")]
        public int InvoiceId { get; set; }

        /// <summary>
        /// معرف الرسم المرتبط
        /// </summary>
        [Required(ErrorMessage = "معرف الرسم مطلوب")]
        public int FeeId { get; set; }

        /// <summary>
        /// اسم العنصر/الرسم
        /// </summary>
        [Required(ErrorMessage = "اسم العنصر مطلوب")]
        [StringLength(100, ErrorMessage = "اسم العنصر يجب أن يكون أقل من 100 حرف")]
        public string ItemName { get; set; } = string.Empty;

        /// <summary>
        /// وصف العنصر
        /// </summary>
        [StringLength(500, ErrorMessage = "وصف العنصر يجب أن يكون أقل من 500 حرف")]
        public string? Description { get; set; }

        /// <summary>
        /// الكمية (عادة 1 للرسوم)
        /// </summary>
        [Required(ErrorMessage = "الكمية مطلوبة")]
        [Range(1, int.MaxValue, ErrorMessage = "الكمية يجب أن تكون أكبر من الصفر")]
        public int Quantity { get; set; } = 1;

        /// <summary>
        /// سعر الوحدة
        /// </summary>
        [Required(ErrorMessage = "سعر الوحدة مطلوب")]
        [Range(0, double.MaxValue, ErrorMessage = "سعر الوحدة يجب أن يكون أكبر من أو يساوي الصفر")]
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// نسبة الخصم (إن وجدت)
        /// </summary>
        [Range(0, 100, ErrorMessage = "نسبة الخصم يجب أن تكون بين 0 و 100")]
        public decimal DiscountPercentage { get; set; } = 0;

        /// <summary>
        /// مبلغ الخصم
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "مبلغ الخصم يجب أن يكون أكبر من أو يساوي الصفر")]
        public decimal DiscountAmount { get; set; } = 0;

        /// <summary>
        /// المبلغ الإجمالي للعنصر (بعد الخصم)
        /// </summary>
        public decimal TotalAmount => (UnitPrice * Quantity) - DiscountAmount;

        /// <summary>
        /// ملاحظات على العنصر
        /// </summary>
        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أنشأ السجل
        /// </summary>
        public int CreatedByUserId { get; set; }

        // Navigation Properties
        public virtual Invoice? Invoice { get; set; }
        public virtual Fee? Fee { get; set; }
    }
}
