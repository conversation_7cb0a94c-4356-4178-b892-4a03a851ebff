using System;
using System.Drawing;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using AlNoorEducationalInstitute.Models.Institution;
using AlNoorEducationalInstitute.Services;

namespace AlNoorEducationalInstitute.Forms
{
    /// <summary>
    /// نافذة إعدادات المؤسسة وإدارة الشعار
    /// Institution settings and logo management form
    /// </summary>
    public partial class InstitutionSettingsForm : Form
    {
        private readonly IInstitutionService _institutionService;
        private readonly ILogger<InstitutionSettingsForm> _logger;
        private readonly int _currentUserId;
        private InstitutionProfile _currentProfile;

        // Controls
        private TabControl tabControl;
        private TabPage tabGeneral;
        private TabPage tabLogo;
        private TabPage tabColors;

        // General Info Controls
        private TextBox txtInstitutionName;
        private TextBox txtInstitutionNameEn;
        private TextBox txtDescription;
        private TextBox txtAddress;
        private TextBox txtCity;
        private TextBox txtPhoneNumber;
        private TextBox txtEmail;
        private TextBox txtWebsite;

        // Logo Controls
        private PictureBox picLogo;
        private Button btnUploadLogo;
        private Button btnDeleteLogo;
        private Label lblLogoInfo;
        private CheckBox chkShowInReports;
        private CheckBox chkShowInInvoices;
        private CheckBox chkShowInCertificates;
        private CheckBox chkShowInLetters;
        private CheckBox chkShowInLoginScreen;
        private CheckBox chkShowInDashboard;

        // Color Controls
        private Panel pnlPrimaryColor;
        private Panel pnlSecondaryColor;
        private Panel pnlTextColor;
        private Button btnPrimaryColor;
        private Button btnSecondaryColor;
        private Button btnTextColor;

        // Action Buttons
        private Button btnSave;
        private Button btnCancel;

        public InstitutionSettingsForm(IServiceProvider serviceProvider, int currentUserId)
        {
            _institutionService = serviceProvider.GetRequiredService<IInstitutionService>();
            _logger = serviceProvider.GetRequiredService<ILogger<InstitutionSettingsForm>>();
            _currentUserId = currentUserId;

            InitializeComponent();
            LoadDataAsync();
        }

        private void InitializeComponent()
        {
            this.Text = "إعدادات المؤسسة";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateControls();
            LayoutControls();
            AttachEvents();
        }

        private void CreateControls()
        {
            // Tab Control
            tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10F)
            };

            // Tabs
            tabGeneral = new TabPage("المعلومات العامة");
            tabLogo = new TabPage("الشعار");
            tabColors = new TabPage("الألوان");

            tabControl.TabPages.AddRange(new TabPage[] { tabGeneral, tabLogo, tabColors });

            // General Info Controls
            txtInstitutionName = new TextBox { Size = new Size(300, 25) };
            txtInstitutionNameEn = new TextBox { Size = new Size(300, 25) };
            txtDescription = new TextBox { Size = new Size(300, 60), Multiline = true };
            txtAddress = new TextBox { Size = new Size(300, 60), Multiline = true };
            txtCity = new TextBox { Size = new Size(300, 25) };
            txtPhoneNumber = new TextBox { Size = new Size(300, 25) };
            txtEmail = new TextBox { Size = new Size(300, 25) };
            txtWebsite = new TextBox { Size = new Size(300, 25) };

            // Logo Controls
            picLogo = new PictureBox
            {
                Size = new Size(200, 150),
                BorderStyle = BorderStyle.FixedSingle,
                SizeMode = PictureBoxSizeMode.Zoom,
                BackColor = Color.LightGray
            };

            btnUploadLogo = new Button
            {
                Text = "رفع شعار جديد",
                Size = new Size(120, 30),
                BackColor = Color.FromArgb(46, 139, 87),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            btnDeleteLogo = new Button
            {
                Text = "حذف الشعار",
                Size = new Size(120, 30),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };

            lblLogoInfo = new Label
            {
                Text = "لم يتم رفع أي شعار",
                Size = new Size(300, 40),
                ForeColor = Color.Gray
            };

            // Logo Display Options
            chkShowInReports = new CheckBox { Text = "إظهار في التقارير", Checked = true };
            chkShowInInvoices = new CheckBox { Text = "إظهار في الفواتير", Checked = true };
            chkShowInCertificates = new CheckBox { Text = "إظهار في الشهادات", Checked = true };
            chkShowInLetters = new CheckBox { Text = "إظهار في الخطابات", Checked = true };
            chkShowInLoginScreen = new CheckBox { Text = "إظهار في شاشة الدخول", Checked = true };
            chkShowInDashboard = new CheckBox { Text = "إظهار في لوحة المعلومات", Checked = true };

            // Color Controls
            pnlPrimaryColor = new Panel { Size = new Size(50, 30), BorderStyle = BorderStyle.FixedSingle, BackColor = ColorTranslator.FromHtml("#2E8B57") };
            pnlSecondaryColor = new Panel { Size = new Size(50, 30), BorderStyle = BorderStyle.FixedSingle, BackColor = ColorTranslator.FromHtml("#FFD700") };
            pnlTextColor = new Panel { Size = new Size(50, 30), BorderStyle = BorderStyle.FixedSingle, BackColor = ColorTranslator.FromHtml("#2F4F4F") };

            btnPrimaryColor = new Button { Text = "تغيير", Size = new Size(60, 25) };
            btnSecondaryColor = new Button { Text = "تغيير", Size = new Size(60, 25) };
            btnTextColor = new Button { Text = "تغيير", Size = new Size(60, 25) };

            // Action Buttons
            btnSave = new Button
            {
                Text = "حفظ",
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            btnCancel = new Button
            {
                Text = "إلغاء",
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                DialogResult = DialogResult.Cancel
            };

            this.Controls.Add(tabControl);
        }

        private void LayoutControls()
        {
            // General Tab Layout
            var generalPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 8,
                Padding = new Padding(20)
            };

            generalPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 150));
            generalPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            generalPanel.Controls.Add(new Label { Text = "اسم المؤسسة:", TextAlign = ContentAlignment.MiddleRight }, 0, 0);
            generalPanel.Controls.Add(txtInstitutionName, 1, 0);
            generalPanel.Controls.Add(new Label { Text = "الاسم الإنجليزي:", TextAlign = ContentAlignment.MiddleRight }, 0, 1);
            generalPanel.Controls.Add(txtInstitutionNameEn, 1, 1);
            generalPanel.Controls.Add(new Label { Text = "الوصف:", TextAlign = ContentAlignment.TopRight }, 0, 2);
            generalPanel.Controls.Add(txtDescription, 1, 2);
            generalPanel.Controls.Add(new Label { Text = "العنوان:", TextAlign = ContentAlignment.TopRight }, 0, 3);
            generalPanel.Controls.Add(txtAddress, 1, 3);
            generalPanel.Controls.Add(new Label { Text = "المدينة:", TextAlign = ContentAlignment.MiddleRight }, 0, 4);
            generalPanel.Controls.Add(txtCity, 1, 4);
            generalPanel.Controls.Add(new Label { Text = "رقم الهاتف:", TextAlign = ContentAlignment.MiddleRight }, 0, 5);
            generalPanel.Controls.Add(txtPhoneNumber, 1, 5);
            generalPanel.Controls.Add(new Label { Text = "البريد الإلكتروني:", TextAlign = ContentAlignment.MiddleRight }, 0, 6);
            generalPanel.Controls.Add(txtEmail, 1, 6);
            generalPanel.Controls.Add(new Label { Text = "الموقع الإلكتروني:", TextAlign = ContentAlignment.MiddleRight }, 0, 7);
            generalPanel.Controls.Add(txtWebsite, 1, 7);

            tabGeneral.Controls.Add(generalPanel);

            // Logo Tab Layout
            var logoPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };
            
            // Logo preview and buttons
            var logoGroup = new GroupBox { Text = "الشعار الحالي", Size = new Size(350, 250), Location = new Point(20, 20) };
            picLogo.Location = new Point(20, 30);
            btnUploadLogo.Location = new Point(20, 190);
            btnDeleteLogo.Location = new Point(150, 190);
            lblLogoInfo.Location = new Point(20, 220);
            
            logoGroup.Controls.AddRange(new Control[] { picLogo, btnUploadLogo, btnDeleteLogo, lblLogoInfo });

            // Display options
            var optionsGroup = new GroupBox { Text = "خيارات العرض", Size = new Size(350, 200), Location = new Point(20, 280) };
            chkShowInReports.Location = new Point(20, 30);
            chkShowInInvoices.Location = new Point(20, 55);
            chkShowInCertificates.Location = new Point(20, 80);
            chkShowInLetters.Location = new Point(180, 30);
            chkShowInLoginScreen.Location = new Point(180, 55);
            chkShowInDashboard.Location = new Point(180, 80);

            optionsGroup.Controls.AddRange(new Control[] { 
                chkShowInReports, chkShowInInvoices, chkShowInCertificates,
                chkShowInLetters, chkShowInLoginScreen, chkShowInDashboard 
            });

            logoPanel.Controls.AddRange(new Control[] { logoGroup, optionsGroup });
            tabLogo.Controls.Add(logoPanel);

            // Colors Tab Layout
            var colorsPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 3,
                Padding = new Padding(20)
            };

            colorsPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 150));
            colorsPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 60));
            colorsPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 70));

            colorsPanel.Controls.Add(new Label { Text = "اللون الأساسي:", TextAlign = ContentAlignment.MiddleRight }, 0, 0);
            colorsPanel.Controls.Add(pnlPrimaryColor, 1, 0);
            colorsPanel.Controls.Add(btnPrimaryColor, 2, 0);

            colorsPanel.Controls.Add(new Label { Text = "اللون الثانوي:", TextAlign = ContentAlignment.MiddleRight }, 0, 1);
            colorsPanel.Controls.Add(pnlSecondaryColor, 1, 1);
            colorsPanel.Controls.Add(btnSecondaryColor, 2, 1);

            colorsPanel.Controls.Add(new Label { Text = "لون النص:", TextAlign = ContentAlignment.MiddleRight }, 0, 2);
            colorsPanel.Controls.Add(pnlTextColor, 1, 2);
            colorsPanel.Controls.Add(btnTextColor, 2, 2);

            tabColors.Controls.Add(colorsPanel);

            // Action buttons at bottom
            var buttonPanel = new Panel { Height = 60, Dock = DockStyle.Bottom };
            btnSave.Location = new Point(this.Width - 220, 15);
            btnCancel.Location = new Point(this.Width - 110, 15);
            buttonPanel.Controls.AddRange(new Control[] { btnSave, btnCancel });
            this.Controls.Add(buttonPanel);
        }

        private void AttachEvents()
        {
            btnUploadLogo.Click += BtnUploadLogo_Click;
            btnDeleteLogo.Click += BtnDeleteLogo_Click;
            btnPrimaryColor.Click += (s, e) => ChooseColor(pnlPrimaryColor);
            btnSecondaryColor.Click += (s, e) => ChooseColor(pnlSecondaryColor);
            btnTextColor.Click += (s, e) => ChooseColor(pnlTextColor);
            btnSave.Click += BtnSave_Click;
        }

        private async void LoadDataAsync()
        {
            try
            {
                _currentProfile = await _institutionService.GetInstitutionProfileAsync();
                PopulateControls();
                await LoadLogoAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل بيانات المؤسسة");
                MessageBox.Show("خطأ في تحميل البيانات", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PopulateControls()
        {
            txtInstitutionName.Text = _currentProfile.InstitutionName;
            txtInstitutionNameEn.Text = _currentProfile.InstitutionNameEn;
            txtDescription.Text = _currentProfile.Description;
            txtAddress.Text = _currentProfile.Address;
            txtCity.Text = _currentProfile.City;
            txtPhoneNumber.Text = _currentProfile.PhoneNumber;
            txtEmail.Text = _currentProfile.Email;
            txtWebsite.Text = _currentProfile.Website;

            chkShowInReports.Checked = _currentProfile.ShowLogoInReports;
            chkShowInInvoices.Checked = _currentProfile.ShowLogoInInvoices;
            chkShowInCertificates.Checked = _currentProfile.ShowLogoInCertificates;
            chkShowInLetters.Checked = _currentProfile.ShowLogoInLetters;
            chkShowInLoginScreen.Checked = _currentProfile.ShowLogoInLoginScreen;
            chkShowInDashboard.Checked = _currentProfile.ShowLogoInDashboard;

            pnlPrimaryColor.BackColor = ColorTranslator.FromHtml(_currentProfile.PrimaryColor);
            pnlSecondaryColor.BackColor = ColorTranslator.FromHtml(_currentProfile.SecondaryColor);
            pnlTextColor.BackColor = ColorTranslator.FromHtml(_currentProfile.TextColor);
        }

        private async Task LoadLogoAsync()
        {
            try
            {
                var logoInfo = await _institutionService.GetLogoInfoAsync();
                if (logoInfo != null && logoInfo.HasLogo)
                {
                    using var ms = new MemoryStream(logoInfo.ImageData);
                    picLogo.Image = Image.FromStream(ms);
                    lblLogoInfo.Text = $"الملف: {logoInfo.FileName}\nالحجم: {logoInfo.FileSizeFormatted}\nتاريخ الرفع: {logoInfo.UploadDate:yyyy/MM/dd}";
                    btnDeleteLogo.Enabled = true;
                }
                else
                {
                    picLogo.Image = null;
                    lblLogoInfo.Text = "لم يتم رفع أي شعار";
                    btnDeleteLogo.Enabled = false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل الشعار");
            }
        }

        private async void BtnUploadLogo_Click(object sender, EventArgs e)
        {
            using var openFileDialog = new OpenFileDialog
            {
                Title = "اختر ملف الشعار",
                Filter = "ملفات الصور|*.png;*.jpg;*.jpeg;*.gif;*.bmp|جميع الملفات|*.*",
                FilterIndex = 1
            };

            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    btnUploadLogo.Enabled = false;
                    btnUploadLogo.Text = "جاري الرفع...";

                    var result = await _institutionService.UploadLogoAsync(openFileDialog.FileName, _currentUserId);

                    if (result.Success)
                    {
                        MessageBox.Show("تم رفع الشعار بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        await LoadLogoAsync();
                    }
                    else
                    {
                        var errorMessage = result.Message;
                        if (result.ValidationErrors.Count > 0)
                        {
                            errorMessage += "\n\nالأخطاء:\n" + string.Join("\n", result.ValidationErrors);
                        }
                        MessageBox.Show(errorMessage, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "خطأ في رفع الشعار");
                    MessageBox.Show("حدث خطأ أثناء رفع الشعار", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                finally
                {
                    btnUploadLogo.Enabled = true;
                    btnUploadLogo.Text = "رفع شعار جديد";
                }
            }
        }

        private async void BtnDeleteLogo_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show(
                "هل أنت متأكد من حذف الشعار؟\nسيتم حذف الشعار من جميع التقارير والمستندات.",
                "تأكيد الحذف",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    btnDeleteLogo.Enabled = false;
                    var success = await _institutionService.DeleteLogoAsync(_currentUserId);

                    if (success)
                    {
                        MessageBox.Show("تم حذف الشعار بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        await LoadLogoAsync();
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف الشعار", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "خطأ في حذف الشعار");
                    MessageBox.Show("حدث خطأ أثناء حذف الشعار", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                finally
                {
                    btnDeleteLogo.Enabled = true;
                }
            }
        }

        private void ChooseColor(Panel colorPanel)
        {
            using var colorDialog = new ColorDialog
            {
                Color = colorPanel.BackColor,
                FullOpen = true
            };

            if (colorDialog.ShowDialog() == DialogResult.OK)
            {
                colorPanel.BackColor = colorDialog.Color;
            }
        }

        private async void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                btnSave.Enabled = false;
                btnSave.Text = "جاري الحفظ...";

                // Update profile data
                _currentProfile.InstitutionName = txtInstitutionName.Text.Trim();
                _currentProfile.InstitutionNameEn = txtInstitutionNameEn.Text.Trim();
                _currentProfile.Description = txtDescription.Text.Trim();
                _currentProfile.Address = txtAddress.Text.Trim();
                _currentProfile.City = txtCity.Text.Trim();
                _currentProfile.PhoneNumber = txtPhoneNumber.Text.Trim();
                _currentProfile.Email = txtEmail.Text.Trim();
                _currentProfile.Website = txtWebsite.Text.Trim();

                _currentProfile.ShowLogoInReports = chkShowInReports.Checked;
                _currentProfile.ShowLogoInInvoices = chkShowInInvoices.Checked;
                _currentProfile.ShowLogoInCertificates = chkShowInCertificates.Checked;
                _currentProfile.ShowLogoInLetters = chkShowInLetters.Checked;
                _currentProfile.ShowLogoInLoginScreen = chkShowInLoginScreen.Checked;
                _currentProfile.ShowLogoInDashboard = chkShowInDashboard.Checked;

                _currentProfile.PrimaryColor = ColorTranslator.ToHtml(pnlPrimaryColor.BackColor);
                _currentProfile.SecondaryColor = ColorTranslator.ToHtml(pnlSecondaryColor.BackColor);
                _currentProfile.TextColor = ColorTranslator.ToHtml(pnlTextColor.BackColor);

                _currentProfile.LastModifiedByUserId = _currentUserId;
                _currentProfile.LastModifiedByUserName = "المستخدم الحالي"; // يمكن تحسينها لاحقاً

                // Validate required fields
                if (string.IsNullOrWhiteSpace(_currentProfile.InstitutionName))
                {
                    MessageBox.Show("اسم المؤسسة مطلوب", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    tabControl.SelectedTab = tabGeneral;
                    txtInstitutionName.Focus();
                    return;
                }

                var success = await _institutionService.UpdateInstitutionProfileAsync(_currentProfile);

                if (success)
                {
                    MessageBox.Show("تم حفظ الإعدادات بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("فشل في حفظ الإعدادات", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حفظ إعدادات المؤسسة");
                MessageBox.Show("حدث خطأ أثناء حفظ الإعدادات", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnSave.Enabled = true;
                btnSave.Text = "حفظ";
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                picLogo?.Image?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
