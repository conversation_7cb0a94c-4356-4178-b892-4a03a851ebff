using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AlNoorEducationalInstitute.Models;

namespace AlNoorEducationalInstitute.Services
{
    /// <summary>
    /// واجهة خدمة إدارة الفصول الدراسية
    /// Class management service interface
    /// </summary>
    public interface IClassService
    {
        /// <summary>
        /// الحصول على جميع الفصول
        /// </summary>
        Task<IEnumerable<Class>> GetAllClassesAsync();

        /// <summary>
        /// الحصول على فصل بواسطة المعرف
        /// </summary>
        Task<Class?> GetClassByIdAsync(int classId);

        /// <summary>
        /// الحصول على الفصول بواسطة المستوى التعليمي
        /// </summary>
        Task<IEnumerable<Class>> GetClassesByLevelAsync(EducationLevel level);

        /// <summary>
        /// الحصول على الفصول بواسطة الصف الدراسي
        /// </summary>
        Task<IEnumerable<Class>> GetClassesByGradeAsync(int grade);

        /// <summary>
        /// الحصول على الفصول بواسطة العام الدراسي
        /// </summary>
        Task<IEnumerable<Class>> GetClassesByAcademicYearAsync(string academicYear);

        /// <summary>
        /// الحصول على الفصول بواسطة المدرس المسؤول
        /// </summary>
        Task<IEnumerable<Class>> GetClassesByTeacherAsync(int teacherId);

        /// <summary>
        /// إضافة فصل جديد
        /// </summary>
        Task<int> AddClassAsync(Class classEntity);

        /// <summary>
        /// تحديث بيانات فصل
        /// </summary>
        Task<bool> UpdateClassAsync(Class classEntity);

        /// <summary>
        /// حذف فصل
        /// </summary>
        Task<bool> DeleteClassAsync(int classId);

        /// <summary>
        /// تغيير حالة الفصل
        /// </summary>
        Task<bool> ChangeClassStatusAsync(int classId, ClassStatus newStatus, int userId);

        /// <summary>
        /// تعيين مدرس مسؤول للفصل
        /// </summary>
        Task<bool> AssignClassTeacherAsync(int classId, int teacherId, int userId);

        /// <summary>
        /// إزالة مدرس مسؤول من الفصل
        /// </summary>
        Task<bool> RemoveClassTeacherAsync(int classId, int userId);

        /// <summary>
        /// التحقق من وجود اسم فصل
        /// </summary>
        Task<bool> IsClassNameExistsAsync(string className, string academicYear, int? excludeClassId = null);

        /// <summary>
        /// الحصول على إحصائيات الفصول
        /// </summary>
        Task<ClassStatistics> GetClassStatisticsAsync();

        /// <summary>
        /// تحديث عدد الطلاب في الفصل
        /// </summary>
        Task<bool> UpdateStudentCountAsync(int classId);
    }

    /// <summary>
    /// إحصائيات الفصول
    /// </summary>
    public class ClassStatistics
    {
        public int TotalClasses { get; set; }
        public int ActiveClasses { get; set; }
        public int ClosedClasses { get; set; }
        public int SuspendedClasses { get; set; }
        public Dictionary<EducationLevel, int> ClassesByLevel { get; set; } = new();
        public Dictionary<int, int> ClassesByGrade { get; set; } = new();
        public int TotalStudents { get; set; }
        public double AverageStudentsPerClass { get; set; }
        public int ClassesWithTeacher { get; set; }
        public int ClassesWithoutTeacher { get; set; }
    }
}
