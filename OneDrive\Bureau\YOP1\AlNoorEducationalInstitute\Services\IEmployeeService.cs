using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AlNoorEducationalInstitute.Models;

namespace AlNoorEducationalInstitute.Services
{
    /// <summary>
    /// واجهة خدمة إدارة الموظفين
    /// Employee management service interface
    /// </summary>
    public interface IEmployeeService
    {
        /// <summary>
        /// الحصول على جميع الموظفين
        /// </summary>
        Task<IEnumerable<Employee>> GetAllEmployeesAsync();

        /// <summary>
        /// الحصول على موظف بواسطة المعرف
        /// </summary>
        Task<Employee?> GetEmployeeByIdAsync(int employeeId);

        /// <summary>
        /// الحصول على موظف بواسطة الرقم الوظيفي
        /// </summary>
        Task<Employee?> GetEmployeeByNumberAsync(string employeeNumber);

        /// <summary>
        /// البحث عن الموظفين بواسطة الاسم
        /// </summary>
        Task<IEnumerable<Employee>> SearchEmployeesByNameAsync(string name);

        /// <summary>
        /// الحصول على الموظفين بواسطة المنصب
        /// </summary>
        Task<IEnumerable<Employee>> GetEmployeesByPositionAsync(EmployeePosition position);

        /// <summary>
        /// الحصول على الموظفين بواسطة القسم
        /// </summary>
        Task<IEnumerable<Employee>> GetEmployeesByDepartmentAsync(string department);

        /// <summary>
        /// الحصول على الموظفين بواسطة الحالة
        /// </summary>
        Task<IEnumerable<Employee>> GetEmployeesByStatusAsync(EmployeeStatus status);

        /// <summary>
        /// الحصول على المدرسين فقط
        /// </summary>
        Task<IEnumerable<Employee>> GetTeachersAsync();

        /// <summary>
        /// إضافة موظف جديد
        /// </summary>
        Task<int> AddEmployeeAsync(Employee employee);

        /// <summary>
        /// تحديث بيانات موظف
        /// </summary>
        Task<bool> UpdateEmployeeAsync(Employee employee);

        /// <summary>
        /// حذف موظف
        /// </summary>
        Task<bool> DeleteEmployeeAsync(int employeeId);

        /// <summary>
        /// تغيير حالة الموظف
        /// </summary>
        Task<bool> ChangeEmployeeStatusAsync(int employeeId, EmployeeStatus newStatus, int userId);

        /// <summary>
        /// التحقق من وجود رقم وظيفي
        /// </summary>
        Task<bool> IsEmployeeNumberExistsAsync(string employeeNumber, int? excludeEmployeeId = null);

        /// <summary>
        /// التحقق من وجود رقم هوية وطنية
        /// </summary>
        Task<bool> IsNationalIdExistsAsync(string nationalId, int? excludeEmployeeId = null);

        /// <summary>
        /// التحقق من وجود بريد إلكتروني
        /// </summary>
        Task<bool> IsEmailExistsAsync(string email, int? excludeEmployeeId = null);

        /// <summary>
        /// الحصول على إحصائيات الموظفين
        /// </summary>
        Task<EmployeeStatistics> GetEmployeeStatisticsAsync();

        /// <summary>
        /// الحصول على الموظفين المعينين في فترة زمنية معينة
        /// </summary>
        Task<IEnumerable<Employee>> GetEmployeesHiredBetweenAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// تصدير بيانات الموظفين
        /// </summary>
        Task<byte[]> ExportEmployeesToExcelAsync(IEnumerable<int>? employeeIds = null);
    }

    /// <summary>
    /// إحصائيات الموظفين
    /// </summary>
    public class EmployeeStatistics
    {
        public int TotalEmployees { get; set; }
        public int ActiveEmployees { get; set; }
        public int SuspendedEmployees { get; set; }
        public int ResignedEmployees { get; set; }
        public int TerminatedEmployees { get; set; }
        public int OnLeaveEmployees { get; set; }
        public int MaleEmployees { get; set; }
        public int FemaleEmployees { get; set; }
        public Dictionary<EmployeePosition, int> EmployeesByPosition { get; set; } = new();
        public Dictionary<string, int> EmployeesByDepartment { get; set; } = new();
        public decimal AverageSalary { get; set; }
        public decimal TotalSalaries { get; set; }
    }
}
