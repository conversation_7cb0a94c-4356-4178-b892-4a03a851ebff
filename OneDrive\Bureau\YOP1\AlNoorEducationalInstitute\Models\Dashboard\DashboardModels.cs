using System;
using System.Collections.Generic;

namespace AlNoorEducationalInstitute.Models.Dashboard
{
    /// <summary>
    /// نموذج مؤشرات الأداء الرئيسية للوحة المعلومات
    /// Dashboard KPIs model
    /// </summary>
    public class DashboardKPIs
    {
        /// <summary>
        /// إجمالي عدد الطلاب
        /// </summary>
        public int TotalStudents { get; set; }

        /// <summary>
        /// عدد الطلاب النشطين
        /// </summary>
        public int ActiveStudents { get; set; }

        /// <summary>
        /// عدد الطلاب الجدد هذا العام
        /// </summary>
        public int NewStudentsThisYear { get; set; }

        /// <summary>
        /// نسبة الحضور اليومي
        /// </summary>
        public decimal DailyAttendanceRate { get; set; }

        /// <summary>
        /// نسبة الحضور الشهري
        /// </summary>
        public decimal MonthlyAttendanceRate { get; set; }

        /// <summary>
        /// إجمالي الإيرادات هذا الشهر
        /// </summary>
        public decimal MonthlyRevenue { get; set; }

        /// <summary>
        /// إجمالي الإيرادات هذا العام
        /// </summary>
        public decimal YearlyRevenue { get; set; }

        /// <summary>
        /// المبالغ المستحقة
        /// </summary>
        public decimal OutstandingAmount { get; set; }

        /// <summary>
        /// نسبة التحصيل المالي
        /// </summary>
        public decimal CollectionRate { get; set; }

        /// <summary>
        /// عدد الفواتير المتأخرة
        /// </summary>
        public int OverdueInvoices { get; set; }

        /// <summary>
        /// المعدل الأكاديمي العام
        /// </summary>
        public decimal OverallAcademicAverage { get; set; }

        /// <summary>
        /// نسبة النجاح العامة
        /// </summary>
        public decimal OverallPassRate { get; set; }

        /// <summary>
        /// عدد الموظفين النشطين
        /// </summary>
        public int ActiveEmployees { get; set; }

        /// <summary>
        /// عدد الفصول النشطة
        /// </summary>
        public int ActiveClasses { get; set; }

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime LastUpdated { get; set; }
    }

    /// <summary>
    /// نموذج توزيع الطلاب حسب المستوى
    /// Student distribution by level model
    /// </summary>
    public class StudentDistribution
    {
        public string LevelName { get; set; } = string.Empty;
        public int StudentCount { get; set; }
        public decimal Percentage { get; set; }
        public string Color { get; set; } = string.Empty;
    }

    /// <summary>
    /// نموذج الحضور اليومي
    /// Daily attendance model
    /// </summary>
    public class DailyAttendance
    {
        public DateTime Date { get; set; }
        public int PresentStudents { get; set; }
        public int AbsentStudents { get; set; }
        public int LateStudents { get; set; }
        public decimal AttendanceRate { get; set; }
    }

    /// <summary>
    /// نموذج الأداء المالي الشهري
    /// Monthly financial performance model
    /// </summary>
    public class MonthlyFinancialPerformance
    {
        public string Month { get; set; } = string.Empty;
        public int MonthNumber { get; set; }
        public decimal Revenue { get; set; }
        public decimal Expenses { get; set; }
        public decimal NetIncome { get; set; }
        public decimal CollectionRate { get; set; }
    }

    /// <summary>
    /// نموذج مقارنة الأداء الأكاديمي بين الفصول
    /// Class academic performance comparison model
    /// </summary>
    public class ClassPerformanceComparison
    {
        public string ClassName { get; set; } = string.Empty;
        public int ClassId { get; set; }
        public decimal AverageGrade { get; set; }
        public decimal AttendanceRate { get; set; }
        public int TotalStudents { get; set; }
        public int PassedStudents { get; set; }
        public decimal PassRate { get; set; }
        public string PerformanceLevel { get; set; } = string.Empty;
    }

    /// <summary>
    /// نموذج الاتجاهات الأكاديمية
    /// Academic trends model
    /// </summary>
    public class AcademicTrends
    {
        public string Period { get; set; } = string.Empty;
        public DateTime Date { get; set; }
        public decimal AverageGrade { get; set; }
        public decimal AttendanceRate { get; set; }
        public decimal PassRate { get; set; }
        public int TotalStudents { get; set; }
    }

    /// <summary>
    /// نموذج الإحصائيات السريعة
    /// Quick stats model
    /// </summary>
    public class QuickStats
    {
        public string Title { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
        public string Trend { get; set; } = string.Empty; // "up", "down", "stable"
        public decimal TrendPercentage { get; set; }
    }

    /// <summary>
    /// نموذج التنبيهات والإشعارات
    /// Alerts and notifications model
    /// </summary>
    public class DashboardAlert
    {
        public int AlertId { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public AlertType Type { get; set; }
        public AlertPriority Priority { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsRead { get; set; }
        public string ActionUrl { get; set; } = string.Empty;
    }

    /// <summary>
    /// نموذج أداء المواد الدراسية
    /// Subject performance model
    /// </summary>
    public class SubjectPerformance
    {
        public string SubjectName { get; set; } = string.Empty;
        public int SubjectId { get; set; }
        public decimal AverageGrade { get; set; }
        public int TotalStudents { get; set; }
        public int PassedStudents { get; set; }
        public decimal PassRate { get; set; }
        public decimal HighestGrade { get; set; }
        public decimal LowestGrade { get; set; }
    }

    /// <summary>
    /// نموذج الأداء المالي حسب نوع الرسوم
    /// Financial performance by fee type model
    /// </summary>
    public class FinancialPerformanceByFeeType
    {
        public string FeeTypeName { get; set; } = string.Empty;
        public decimal TotalAmount { get; set; }
        public decimal CollectedAmount { get; set; }
        public decimal OutstandingAmount { get; set; }
        public decimal CollectionRate { get; set; }
        public int TotalInvoices { get; set; }
        public int PaidInvoices { get; set; }
    }

    /// <summary>
    /// تعداد أنواع التنبيهات
    /// </summary>
    public enum AlertType
    {
        Info = 1,
        Warning = 2,
        Error = 3,
        Success = 4
    }

    /// <summary>
    /// تعداد أولوية التنبيهات
    /// </summary>
    public enum AlertPriority
    {
        Low = 1,
        Medium = 2,
        High = 3,
        Critical = 4
    }

    /// <summary>
    /// نموذج فلتر لوحة المعلومات
    /// Dashboard filter model
    /// </summary>
    public class DashboardFilter
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public int? ClassId { get; set; }
        public EducationLevel? Level { get; set; }
        public Semester? Semester { get; set; }
        public string AcademicYear { get; set; } = string.Empty;
        public bool IncludeInactive { get; set; } = false;
    }

    /// <summary>
    /// نموذج إعدادات لوحة المعلومات
    /// Dashboard settings model
    /// </summary>
    public class DashboardSettings
    {
        public int UserId { get; set; }
        public bool ShowFinancialKPIs { get; set; } = true;
        public bool ShowAcademicKPIs { get; set; } = true;
        public bool ShowAttendanceChart { get; set; } = true;
        public bool ShowRevenueChart { get; set; } = true;
        public bool ShowClassComparison { get; set; } = true;
        public bool ShowAlerts { get; set; } = true;
        public int RefreshInterval { get; set; } = 300; // seconds
        public string DefaultAcademicYear { get; set; } = string.Empty;
        public Semester DefaultSemester { get; set; } = Semester.First;
        public DateTime LastModified { get; set; }
    }
}
