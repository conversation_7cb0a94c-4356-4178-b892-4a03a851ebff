using System;
using System.ComponentModel.DataAnnotations;

namespace AlNoorEducationalInstitute.Models
{
    /// <summary>
    /// نموذج بيانات المستخدم
    /// User data model
    /// </summary>
    public class User
    {
        /// <summary>
        /// المعرف الفريد للمستخدم
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// اسم المستخدم (للدخول إلى النظام)
        /// </summary>
        [Required(ErrorMessage = "اسم المستخدم مطلوب")]
        [StringLength(50, ErrorMessage = "اسم المستخدم يجب أن يكون أقل من 50 حرف")]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// كلمة المرور المشفرة
        /// </summary>
        [Required(ErrorMessage = "كلمة المرور مطلوبة")]
        public string PasswordHash { get; set; } = string.Empty;

        /// <summary>
        /// الاسم الكامل للمستخدم
        /// </summary>
        [Required(ErrorMessage = "الاسم الكامل مطلوب")]
        [StringLength(100, ErrorMessage = "الاسم الكامل يجب أن يكون أقل من 100 حرف")]
        public string FullName { get; set; } = string.Empty;

        /// <summary>
        /// البريد الإلكتروني
        /// </summary>
        [Required(ErrorMessage = "البريد الإلكتروني مطلوب")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [StringLength(100, ErrorMessage = "البريد الإلكتروني يجب أن يكون أقل من 100 حرف")]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// رقم الهاتف
        /// </summary>
        [StringLength(20, ErrorMessage = "رقم الهاتف يجب أن يكون أقل من 20 رقم")]
        public string? Phone { get; set; }

        /// <summary>
        /// دور المستخدم في النظام
        /// </summary>
        [Required(ErrorMessage = "دور المستخدم مطلوب")]
        public UserRole Role { get; set; }

        /// <summary>
        /// الصلاحيات المخصصة للمستخدم (JSON)
        /// </summary>
        public string? Permissions { get; set; }

        /// <summary>
        /// حالة المستخدم (نشط، معطل، محظور)
        /// </summary>
        public UserStatus Status { get; set; } = UserStatus.Active;

        /// <summary>
        /// تاريخ آخر دخول للنظام
        /// </summary>
        public DateTime? LastLoginDate { get; set; }

        /// <summary>
        /// عدد محاولات الدخول الفاشلة
        /// </summary>
        public int FailedLoginAttempts { get; set; } = 0;

        /// <summary>
        /// تاريخ قفل الحساب (إذا كان مقفلاً)
        /// </summary>
        public DateTime? AccountLockedUntil { get; set; }

        /// <summary>
        /// تاريخ انتهاء صلاحية كلمة المرور
        /// </summary>
        public DateTime? PasswordExpiryDate { get; set; }

        /// <summary>
        /// هل يجب على المستخدم تغيير كلمة المرور في الدخول التالي؟
        /// </summary>
        public bool MustChangePassword { get; set; } = false;

        /// <summary>
        /// معرف الموظف المرتبط بهذا المستخدم (إذا كان موظفاً)
        /// </summary>
        public int? EmployeeId { get; set; }

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime LastModifiedDate { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أنشأ هذا الحساب
        /// </summary>
        public int CreatedByUserId { get; set; }

        /// <summary>
        /// معرف المستخدم الذي عدل هذا الحساب آخر مرة
        /// </summary>
        public int LastModifiedByUserId { get; set; }

        // Navigation Properties
        public virtual Employee? Employee { get; set; }
    }

    /// <summary>
    /// تعداد أدوار المستخدمين
    /// </summary>
    public enum UserRole
    {
        SuperAdmin = 1,         // مدير النظام الرئيسي
        Admin = 2,              // مدير النظام
        Director = 3,           // مدير المؤسسة
        ViceDirector = 4,       // نائب المدير
        Teacher = 5,            // مدرس
        Administrator = 6,      // موظف إداري
        Accountant = 7,         // محاسب
        Receptionist = 8,       // موظف استقبال
        Librarian = 9,          // أمين مكتبة
        ITSpecialist = 10,      // أخصائي تقنية معلومات
        Counselor = 11,         // مرشد طلابي
        ReadOnly = 12,          // قراءة فقط
        Guest = 13              // ضيف
    }

    /// <summary>
    /// تعداد حالة المستخدم
    /// </summary>
    public enum UserStatus
    {
        Active = 1,         // نشط
        Inactive = 2,       // غير نشط
        Suspended = 3,      // معلق
        Locked = 4,         // مقفل
        Banned = 5          // محظور
    }

    /// <summary>
    /// فئة الصلاحيات
    /// </summary>
    public class UserPermissions
    {
        public bool CanViewStudents { get; set; } = false;
        public bool CanAddStudents { get; set; } = false;
        public bool CanEditStudents { get; set; } = false;
        public bool CanDeleteStudents { get; set; } = false;
        public bool CanArchiveStudents { get; set; } = false;

        public bool CanViewEmployees { get; set; } = false;
        public bool CanAddEmployees { get; set; } = false;
        public bool CanEditEmployees { get; set; } = false;
        public bool CanDeleteEmployees { get; set; } = false;

        public bool CanViewClasses { get; set; } = false;
        public bool CanAddClasses { get; set; } = false;
        public bool CanEditClasses { get; set; } = false;
        public bool CanDeleteClasses { get; set; } = false;

        public bool CanViewSubjects { get; set; } = false;
        public bool CanAddSubjects { get; set; } = false;
        public bool CanEditSubjects { get; set; } = false;
        public bool CanDeleteSubjects { get; set; } = false;

        public bool CanViewUsers { get; set; } = false;
        public bool CanAddUsers { get; set; } = false;
        public bool CanEditUsers { get; set; } = false;
        public bool CanDeleteUsers { get; set; } = false;

        public bool CanViewReports { get; set; } = false;
        public bool CanExportData { get; set; } = false;
        public bool CanBackupDatabase { get; set; } = false;
        public bool CanRestoreDatabase { get; set; } = false;

        public bool CanViewSystemSettings { get; set; } = false;
        public bool CanEditSystemSettings { get; set; } = false;
    }
}
