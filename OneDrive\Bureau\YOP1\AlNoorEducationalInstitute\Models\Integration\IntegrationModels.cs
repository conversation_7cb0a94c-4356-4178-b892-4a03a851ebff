using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AlNoorEducationalInstitute.Models.Integration
{
    /// <summary>
    /// نموذج إعدادات بوابة الرسائل القصيرة
    /// SMS Gateway settings model
    /// </summary>
    public class SmsGatewaySettings
    {
        public int SettingsId { get; set; }
        public string ProviderName { get; set; } = string.Empty;
        public string ApiUrl { get; set; } = string.Empty;
        public string ApiKey { get; set; } = string.Empty;
        public string Username { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public string SenderId { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
        public int MaxMessageLength { get; set; } = 160;
        public decimal CostPerMessage { get; set; }
        public string SupportedLanguages { get; set; } = "ar,en";
        public DateTime CreatedDate { get; set; }
        public DateTime LastModifiedDate { get; set; }
    }

    /// <summary>
    /// نموذج رسالة قصيرة
    /// SMS message model
    /// </summary>
    public class SmsMessage
    {
        public int MessageId { get; set; }
        
        [Required(ErrorMessage = "رقم الهاتف مطلوب")]
        [Phone(ErrorMessage = "رقم الهاتف غير صحيح")]
        public string PhoneNumber { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "نص الرسالة مطلوب")]
        [StringLength(500, ErrorMessage = "نص الرسالة يجب أن يكون أقل من 500 حرف")]
        public string MessageText { get; set; } = string.Empty;
        
        public SmsMessageType MessageType { get; set; }
        public SmsMessagePriority Priority { get; set; } = SmsMessagePriority.Normal;
        public DateTime ScheduledDate { get; set; }
        public DateTime? SentDate { get; set; }
        public SmsMessageStatus Status { get; set; } = SmsMessageStatus.Pending;
        public string StatusMessage { get; set; } = string.Empty;
        public string ExternalMessageId { get; set; } = string.Empty;
        public int RetryCount { get; set; } = 0;
        public int MaxRetries { get; set; } = 3;
        public decimal Cost { get; set; }
        public int CreatedByUserId { get; set; }
        public DateTime CreatedDate { get; set; }
        
        // Navigation properties
        public int? StudentId { get; set; }
        public int? EmployeeId { get; set; }
        public string RecipientName { get; set; } = string.Empty;
        public string RecipientType { get; set; } = string.Empty;
    }

    /// <summary>
    /// نموذج رسالة جماعية
    /// Bulk SMS message model
    /// </summary>
    public class BulkSmsMessage
    {
        public int BulkMessageId { get; set; }
        
        [Required(ErrorMessage = "عنوان الحملة مطلوب")]
        public string CampaignName { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "نص الرسالة مطلوب")]
        public string MessageText { get; set; } = string.Empty;
        
        public SmsMessageType MessageType { get; set; }
        public BulkMessageTarget TargetType { get; set; }
        public string TargetCriteria { get; set; } = string.Empty; // JSON criteria
        public DateTime ScheduledDate { get; set; }
        public DateTime? StartedDate { get; set; }
        public DateTime? CompletedDate { get; set; }
        public BulkMessageStatus Status { get; set; } = BulkMessageStatus.Draft;
        
        public int TotalRecipients { get; set; }
        public int SentCount { get; set; }
        public int DeliveredCount { get; set; }
        public int FailedCount { get; set; }
        public decimal TotalCost { get; set; }
        
        public int CreatedByUserId { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime LastModifiedDate { get; set; }
        
        public List<SmsMessage> Messages { get; set; } = new();
    }

    /// <summary>
    /// نموذج إعدادات بوابة الدفع الإلكتروني
    /// Payment gateway settings model
    /// </summary>
    public class PaymentGatewaySettings
    {
        public int SettingsId { get; set; }
        public string ProviderName { get; set; } = string.Empty;
        public PaymentGatewayType GatewayType { get; set; }
        public string ApiUrl { get; set; } = string.Empty;
        public string MerchantId { get; set; } = string.Empty;
        public string ApiKey { get; set; } = string.Empty;
        public string SecretKey { get; set; } = string.Empty;
        public string PublicKey { get; set; } = string.Empty;
        public bool IsTestMode { get; set; } = true;
        public bool IsActive { get; set; } = false;
        public string SupportedCurrencies { get; set; } = "SAR";
        public string SupportedPaymentMethods { get; set; } = "VISA,MASTERCARD,MADA";
        public decimal MinAmount { get; set; } = 1;
        public decimal MaxAmount { get; set; } = 100000;
        public decimal TransactionFeePercentage { get; set; }
        public decimal FixedTransactionFee { get; set; }
        public string CallbackUrl { get; set; } = string.Empty;
        public string ReturnUrl { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; }
        public DateTime LastModifiedDate { get; set; }
    }

    /// <summary>
    /// نموذج معاملة الدفع الإلكتروني
    /// Online payment transaction model
    /// </summary>
    public class OnlinePaymentTransaction
    {
        public int TransactionId { get; set; }
        
        [Required(ErrorMessage = "رقم المعاملة مطلوب")]
        public string TransactionNumber { get; set; } = string.Empty;
        
        public int InvoiceId { get; set; }
        public int StudentId { get; set; }
        public string PayerName { get; set; } = string.Empty;
        public string PayerEmail { get; set; } = string.Empty;
        public string PayerPhone { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "مبلغ المعاملة مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "مبلغ المعاملة يجب أن يكون أكبر من الصفر")]
        public decimal Amount { get; set; }
        
        public string Currency { get; set; } = "SAR";
        public PaymentGatewayType GatewayType { get; set; }
        public string PaymentMethod { get; set; } = string.Empty;
        public OnlinePaymentStatus Status { get; set; } = OnlinePaymentStatus.Pending;
        
        public string GatewayTransactionId { get; set; } = string.Empty;
        public string GatewayReference { get; set; } = string.Empty;
        public string AuthorizationCode { get; set; } = string.Empty;
        
        public DateTime InitiatedDate { get; set; }
        public DateTime? ProcessedDate { get; set; }
        public DateTime? CompletedDate { get; set; }
        
        public decimal TransactionFee { get; set; }
        public decimal NetAmount { get; set; }
        
        public string StatusMessage { get; set; } = string.Empty;
        public string GatewayResponse { get; set; } = string.Empty; // JSON response
        public string FailureReason { get; set; } = string.Empty;
        
        public int RetryCount { get; set; } = 0;
        public int MaxRetries { get; set; } = 3;
        
        public DateTime CreatedDate { get; set; }
        public DateTime LastModifiedDate { get; set; }
        
        // Navigation properties
        public virtual Invoice? Invoice { get; set; }
        public virtual Student? Student { get; set; }
    }

    /// <summary>
    /// نموذج إشعار الدفع
    /// Payment notification model
    /// </summary>
    public class PaymentNotification
    {
        public int NotificationId { get; set; }
        public int TransactionId { get; set; }
        public string NotificationType { get; set; } = string.Empty;
        public string NotificationData { get; set; } = string.Empty; // JSON data
        public DateTime ReceivedDate { get; set; }
        public bool IsProcessed { get; set; } = false;
        public DateTime? ProcessedDate { get; set; }
        public string ProcessingResult { get; set; } = string.Empty;
        
        public virtual OnlinePaymentTransaction? Transaction { get; set; }
    }

    /// <summary>
    /// نموذج قالب الرسائل
    /// Message template model
    /// </summary>
    public class MessageTemplate
    {
        public int TemplateId { get; set; }
        
        [Required(ErrorMessage = "اسم القالب مطلوب")]
        public string TemplateName { get; set; } = string.Empty;
        
        public string Description { get; set; } = string.Empty;
        public SmsMessageType MessageType { get; set; }
        
        [Required(ErrorMessage = "نص القالب مطلوب")]
        public string TemplateText { get; set; } = string.Empty;
        
        public string Variables { get; set; } = string.Empty; // JSON array of variables
        public bool IsActive { get; set; } = true;
        public bool IsDefault { get; set; } = false;
        
        public int CreatedByUserId { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime LastModifiedDate { get; set; }
    }

    /// <summary>
    /// نموذج سجل التكامل
    /// Integration log model
    /// </summary>
    public class IntegrationLog
    {
        public int LogId { get; set; }
        public IntegrationType IntegrationType { get; set; }
        public string ServiceName { get; set; } = string.Empty;
        public string Operation { get; set; } = string.Empty;
        public string RequestData { get; set; } = string.Empty;
        public string ResponseData { get; set; } = string.Empty;
        public bool IsSuccess { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public TimeSpan Duration { get; set; }
        public DateTime CreatedDate { get; set; }
        
        public int? RelatedEntityId { get; set; }
        public string RelatedEntityType { get; set; } = string.Empty;
    }

    // Enums
    public enum SmsMessageType
    {
        General = 1,
        Attendance = 2,
        Grades = 3,
        Financial = 4,
        Emergency = 5,
        Reminder = 6,
        Welcome = 7,
        Graduation = 8,
        Event = 9,
        Marketing = 10
    }

    public enum SmsMessagePriority
    {
        Low = 1,
        Normal = 2,
        High = 3,
        Urgent = 4
    }

    public enum SmsMessageStatus
    {
        Pending = 1,
        Queued = 2,
        Sending = 3,
        Sent = 4,
        Delivered = 5,
        Failed = 6,
        Cancelled = 7
    }

    public enum BulkMessageTarget
    {
        AllStudents = 1,
        AllParents = 2,
        AllEmployees = 3,
        SpecificClass = 4,
        SpecificGrade = 5,
        SpecificLevel = 6,
        CustomList = 7
    }

    public enum BulkMessageStatus
    {
        Draft = 1,
        Scheduled = 2,
        InProgress = 3,
        Completed = 4,
        Failed = 5,
        Cancelled = 6
    }

    public enum PaymentGatewayType
    {
        PayTabs = 1,
        HyperPay = 2,
        PayFort = 3,
        Mada = 4,
        STC_Pay = 5,
        Urpay = 6,
        Moyasar = 7,
        Tap = 8
    }

    public enum OnlinePaymentStatus
    {
        Pending = 1,
        Processing = 2,
        Authorized = 3,
        Captured = 4,
        Completed = 5,
        Failed = 6,
        Cancelled = 7,
        Refunded = 8,
        PartiallyRefunded = 9
    }

    public enum IntegrationType
    {
        SMS = 1,
        Payment = 2,
        Email = 3,
        API = 4,
        Webhook = 5
    }
}
