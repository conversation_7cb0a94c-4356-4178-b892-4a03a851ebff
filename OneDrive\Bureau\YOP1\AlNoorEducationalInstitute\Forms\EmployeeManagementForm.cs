using System;
using System.Drawing;
using System.Windows.Forms;
using AlNoorEducationalInstitute.Services;
using AlNoorEducationalInstitute.Models;

namespace AlNoorEducationalInstitute.Forms
{
    /// <summary>
    /// نافذة إدارة الموظفين
    /// Employee management form
    /// </summary>
    public partial class EmployeeManagementForm : Form
    {
        private readonly IEmployeeService _employeeService;
        private DataGridView dgvEmployees;
        private Panel pnlTop;
        private Panel pnlBottom;
        private TextBox txtSearch;
        private Button btnSearch;
        private Button btnAdd;
        private Button btnEdit;
        private Button btnDelete;
        private Button btnRefresh;
        private Label lblSearch;
        private ComboBox cmbPosition;
        private Label lblPosition;

        public EmployeeManagementForm(IEmployeeService employeeService)
        {
            _employeeService = employeeService;
            InitializeComponent();
            SetupForm();
            LoadEmployees();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Text = "إدارة الموظفين";
            this.Size = new Size(1000, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.Font = new Font("Tahoma", 10F, FontStyle.Regular);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Top panel
            pnlTop = new Panel
            {
                Height = 60,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(240, 240, 240),
                Padding = new Padding(10)
            };

            // Search controls
            lblSearch = new Label
            {
                Text = "البحث:",
                Size = new Size(50, 25),
                Location = new Point(920, 15),
                TextAlign = ContentAlignment.MiddleRight
            };

            txtSearch = new TextBox
            {
                Size = new Size(200, 25),
                Location = new Point(710, 15),
                PlaceholderText = "ابحث بالاسم أو الرقم الوظيفي"
            };

            btnSearch = new Button
            {
                Text = "بحث",
                Size = new Size(60, 25),
                Location = new Point(640, 15),
                BackColor = Color.FromArgb(70, 130, 180),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            lblPosition = new Label
            {
                Text = "المنصب:",
                Size = new Size(50, 25),
                Location = new Point(580, 15),
                TextAlign = ContentAlignment.MiddleRight
            };

            cmbPosition = new ComboBox
            {
                Size = new Size(120, 25),
                Location = new Point(450, 15),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            pnlTop.Controls.AddRange(new Control[]
            {
                lblSearch, txtSearch, btnSearch, lblPosition, cmbPosition
            });

            // Bottom panel
            pnlBottom = new Panel
            {
                Height = 60,
                Dock = DockStyle.Bottom,
                BackColor = Color.FromArgb(240, 240, 240),
                Padding = new Padding(10)
            };

            // Buttons
            btnAdd = new Button
            {
                Text = "إضافة موظف",
                Size = new Size(100, 35),
                Location = new Point(10, 12),
                BackColor = Color.FromArgb(34, 139, 34),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            btnEdit = new Button
            {
                Text = "تعديل",
                Size = new Size(80, 35),
                Location = new Point(120, 12),
                BackColor = Color.FromArgb(255, 165, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            btnDelete = new Button
            {
                Text = "حذف",
                Size = new Size(80, 35),
                Location = new Point(210, 12),
                BackColor = Color.FromArgb(220, 20, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            btnRefresh = new Button
            {
                Text = "تحديث",
                Size = new Size(80, 35),
                Location = new Point(300, 12),
                BackColor = Color.FromArgb(70, 130, 180),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            pnlBottom.Controls.AddRange(new Control[]
            {
                btnAdd, btnEdit, btnDelete, btnRefresh
            });

            // DataGridView
            dgvEmployees = new DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize,
                RowHeadersVisible = false
            };

            // Add controls to form
            this.Controls.Add(dgvEmployees);
            this.Controls.Add(pnlBottom);
            this.Controls.Add(pnlTop);

            this.ResumeLayout(false);
        }

        private void SetupForm()
        {
            // Setup position combobox
            cmbPosition.Items.Add(new ComboBoxItem("الكل", null));
            cmbPosition.Items.Add(new ComboBoxItem("مدير", EmployeePosition.Director));
            cmbPosition.Items.Add(new ComboBoxItem("نائب مدير", EmployeePosition.ViceDirector));
            cmbPosition.Items.Add(new ComboBoxItem("مدرس", EmployeePosition.Teacher));
            cmbPosition.Items.Add(new ComboBoxItem("موظف إداري", EmployeePosition.Administrator));
            cmbPosition.Items.Add(new ComboBoxItem("محاسب", EmployeePosition.Accountant));
            cmbPosition.Items.Add(new ComboBoxItem("موظف استقبال", EmployeePosition.Receptionist));
            cmbPosition.SelectedIndex = 0;

            // Setup DataGridView columns
            SetupDataGridViewColumns();

            // Event handlers
            btnSearch.Click += BtnSearch_Click;
            btnAdd.Click += BtnAdd_Click;
            btnEdit.Click += BtnEdit_Click;
            btnDelete.Click += BtnDelete_Click;
            btnRefresh.Click += BtnRefresh_Click;
            txtSearch.KeyPress += TxtSearch_KeyPress;
            cmbPosition.SelectedIndexChanged += CmbPosition_SelectedIndexChanged;
            dgvEmployees.CellDoubleClick += DgvEmployees_CellDoubleClick;

            // Button styling
            foreach (Button btn in new[] { btnSearch, btnAdd, btnEdit, btnDelete, btnRefresh })
            {
                btn.FlatAppearance.BorderSize = 0;
            }
        }

        private void SetupDataGridViewColumns()
        {
            dgvEmployees.Columns.Clear();

            dgvEmployees.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "EmployeeId",
                HeaderText = "المعرف",
                Visible = false
            });

            dgvEmployees.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "EmployeeNumber",
                HeaderText = "الرقم الوظيفي",
                Width = 100
            });

            dgvEmployees.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "FullNameArabic",
                HeaderText = "الاسم الكامل",
                Width = 200
            });

            dgvEmployees.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Position",
                HeaderText = "المنصب",
                Width = 120
            });

            dgvEmployees.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Department",
                HeaderText = "القسم",
                Width = 100
            });

            dgvEmployees.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Phone",
                HeaderText = "الهاتف",
                Width = 120
            });

            dgvEmployees.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Email",
                HeaderText = "البريد الإلكتروني",
                Width = 150
            });

            dgvEmployees.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "HireDate",
                HeaderText = "تاريخ التعيين",
                Width = 100
            });

            dgvEmployees.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Status",
                HeaderText = "الحالة",
                Width = 80
            });
        }

        private async void LoadEmployees()
        {
            try
            {
                dgvEmployees.Rows.Clear();
                
                var employees = await _employeeService.GetAllEmployeesAsync();
                
                foreach (var employee in employees)
                {
                    var row = new DataGridViewRow();
                    row.CreateCells(dgvEmployees);
                    
                    row.Cells["EmployeeId"].Value = employee.EmployeeId;
                    row.Cells["EmployeeNumber"].Value = employee.EmployeeNumber;
                    row.Cells["FullNameArabic"].Value = employee.FullNameArabic;
                    row.Cells["Position"].Value = GetPositionDisplayName(employee.Position);
                    row.Cells["Department"].Value = employee.Department ?? "غير محدد";
                    row.Cells["Phone"].Value = employee.Phone;
                    row.Cells["Email"].Value = employee.Email;
                    row.Cells["HireDate"].Value = employee.HireDate.ToString("yyyy/MM/dd");
                    row.Cells["Status"].Value = GetStatusDisplayName(employee.Status);
                    
                    dgvEmployees.Rows.Add(row);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الموظفين: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private string GetPositionDisplayName(EmployeePosition position)
        {
            return position switch
            {
                EmployeePosition.Director => "مدير",
                EmployeePosition.ViceDirector => "نائب مدير",
                EmployeePosition.Teacher => "مدرس",
                EmployeePosition.Administrator => "موظف إداري",
                EmployeePosition.Accountant => "محاسب",
                EmployeePosition.Receptionist => "موظف استقبال",
                EmployeePosition.Librarian => "أمين مكتبة",
                EmployeePosition.ITSpecialist => "أخصائي تقنية معلومات",
                EmployeePosition.Counselor => "مرشد طلابي",
                EmployeePosition.Nurse => "ممرض",
                EmployeePosition.SecurityGuard => "حارس أمن",
                EmployeePosition.Janitor => "عامل نظافة",
                EmployeePosition.Driver => "سائق",
                EmployeePosition.Other => "أخرى",
                _ => "غير محدد"
            };
        }

        private string GetStatusDisplayName(EmployeeStatus status)
        {
            return status switch
            {
                EmployeeStatus.Active => "نشط",
                EmployeeStatus.Suspended => "معلق",
                EmployeeStatus.Resigned => "مستقيل",
                EmployeeStatus.Terminated => "مفصول",
                EmployeeStatus.OnLeave => "في إجازة",
                _ => "غير محدد"
            };
        }

        // Event handlers
        private void TxtSearch_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                BtnSearch_Click(sender, e);
                e.Handled = true;
            }
        }

        private void BtnSearch_Click(object sender, EventArgs e) => PerformSearch();
        private void CmbPosition_SelectedIndexChanged(object sender, EventArgs e) => PerformSearch();
        private void BtnRefresh_Click(object sender, EventArgs e) => LoadEmployees();

        private void PerformSearch()
        {
            MessageBox.Show("سيتم تطبيق وظيفة البحث لاحقاً", "معلومات", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم فتح نافذة إضافة موظف جديد", "معلومات", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (dgvEmployees.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار موظف للتعديل", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            MessageBox.Show("سيتم فتح نافذة تعديل بيانات الموظف", "معلومات", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            if (dgvEmployees.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار موظف للحذف", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show("هل تريد حذف الموظف المحدد؟", "تأكيد الحذف", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                MessageBox.Show("سيتم تطبيق وظيفة الحذف لاحقاً", "معلومات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void DgvEmployees_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                BtnEdit_Click(sender, e);
            }
        }
    }
}
