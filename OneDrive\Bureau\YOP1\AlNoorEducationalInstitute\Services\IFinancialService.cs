using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AlNoorEducationalInstitute.Models;

namespace AlNoorEducationalInstitute.Services
{
    /// <summary>
    /// واجهة خدمة النظام المالي
    /// Financial service interface
    /// </summary>
    public interface IFinancialService
    {
        // إدارة الرسوم
        Task<IEnumerable<Fee>> GetAllFeesAsync();
        Task<Fee?> GetFeeByIdAsync(int feeId);
        Task<IEnumerable<Fee>> GetFeesByTypeAsync(FeeType type);
        Task<IEnumerable<Fee>> GetFeesByLevelAsync(EducationLevel level);
        Task<IEnumerable<Fee>> GetActiveFeesAsync();
        Task<int> AddFeeAsync(Fee fee);
        Task<bool> UpdateFeeAsync(Fee fee);
        Task<bool> DeleteFeeAsync(int feeId);
        Task<bool> ActivateFeeAsync(int feeId, int userId);
        Task<bool> DeactivateFeeAsync(int feeId, int userId);

        // إدارة الفواتير
        Task<IEnumerable<Invoice>> GetAllInvoicesAsync();
        Task<Invoice?> GetInvoiceByIdAsync(int invoiceId);
        Task<Invoice?> GetInvoiceByNumberAsync(string invoiceNumber);
        Task<IEnumerable<Invoice>> GetInvoicesByStudentAsync(int studentId);
        Task<IEnumerable<Invoice>> GetInvoicesByStatusAsync(InvoiceStatus status);
        Task<IEnumerable<Invoice>> GetOverdueInvoicesAsync();
        Task<int> AddInvoiceAsync(Invoice invoice);
        Task<bool> UpdateInvoiceAsync(Invoice invoice);
        Task<bool> DeleteInvoiceAsync(int invoiceId);
        Task<string> GenerateInvoiceNumberAsync();

        // إدارة عناصر الفاتورة
        Task<IEnumerable<InvoiceItem>> GetInvoiceItemsAsync(int invoiceId);
        Task<int> AddInvoiceItemAsync(InvoiceItem item);
        Task<bool> UpdateInvoiceItemAsync(InvoiceItem item);
        Task<bool> DeleteInvoiceItemAsync(int itemId);

        // إدارة الدفعات
        Task<IEnumerable<Payment>> GetAllPaymentsAsync();
        Task<Payment?> GetPaymentByIdAsync(int paymentId);
        Task<IEnumerable<Payment>> GetPaymentsByInvoiceAsync(int invoiceId);
        Task<IEnumerable<Payment>> GetPaymentsByStudentAsync(int studentId);
        Task<IEnumerable<Payment>> GetPaymentsByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<int> AddPaymentAsync(Payment payment);
        Task<bool> UpdatePaymentAsync(Payment payment);
        Task<bool> DeletePaymentAsync(int paymentId);
        Task<string> GeneratePaymentNumberAsync();
        Task<string> GenerateReceiptNumberAsync();

        // العمليات المالية
        Task<bool> ProcessPaymentAsync(int invoiceId, decimal amount, PaymentMethod method, string payerName, int userId);
        Task<decimal> CalculateInvoiceTotalAsync(int invoiceId);
        Task<bool> UpdateInvoiceStatusAsync(int invoiceId);
        Task<Invoice> GenerateMonthlyInvoiceAsync(int studentId, int month, int year, int userId);
        Task<IEnumerable<Invoice>> GenerateBulkMonthlyInvoicesAsync(int classId, int month, int year, int userId);

        // التقارير المالية
        Task<FinancialSummary> GetFinancialSummaryAsync(DateTime startDate, DateTime endDate);
        Task<IEnumerable<StudentFinancialStatus>> GetStudentsFinancialStatusAsync();
        Task<StudentFinancialStatus> GetStudentFinancialStatusAsync(int studentId);
        Task<decimal> GetTotalRevenueAsync(DateTime startDate, DateTime endDate);
        Task<decimal> GetOutstandingAmountAsync();
        Task<IEnumerable<OverdueInvoiceReport>> GetOverdueInvoicesReportAsync();

        // تصدير البيانات
        Task<byte[]> ExportInvoicesToExcelAsync(DateTime startDate, DateTime endDate);
        Task<byte[]> ExportPaymentsToExcelAsync(DateTime startDate, DateTime endDate);
        Task<byte[]> ExportFinancialReportToExcelAsync(DateTime startDate, DateTime endDate);
    }

    /// <summary>
    /// ملخص مالي
    /// </summary>
    public class FinancialSummary
    {
        public decimal TotalRevenue { get; set; }
        public decimal TotalOutstanding { get; set; }
        public decimal TotalPaid { get; set; }
        public int TotalInvoices { get; set; }
        public int PaidInvoices { get; set; }
        public int OverdueInvoices { get; set; }
        public int PartiallyPaidInvoices { get; set; }
        public Dictionary<PaymentMethod, decimal> PaymentsByMethod { get; set; } = new();
        public Dictionary<FeeType, decimal> RevenueByFeeType { get; set; } = new();
    }

    /// <summary>
    /// حالة الطالب المالية
    /// </summary>
    public class StudentFinancialStatus
    {
        public int StudentId { get; set; }
        public string StudentName { get; set; } = string.Empty;
        public string StudentNumber { get; set; } = string.Empty;
        public string ClassName { get; set; } = string.Empty;
        public decimal TotalInvoiced { get; set; }
        public decimal TotalPaid { get; set; }
        public decimal Outstanding { get; set; }
        public int OverdueInvoices { get; set; }
        public DateTime? LastPaymentDate { get; set; }
        public string GuardianName { get; set; } = string.Empty;
        public string GuardianPhone { get; set; } = string.Empty;
    }

    /// <summary>
    /// تقرير الفواتير المتأخرة
    /// </summary>
    public class OverdueInvoiceReport
    {
        public int InvoiceId { get; set; }
        public string InvoiceNumber { get; set; } = string.Empty;
        public int StudentId { get; set; }
        public string StudentName { get; set; } = string.Empty;
        public string StudentNumber { get; set; } = string.Empty;
        public string ClassName { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public DateTime DueDate { get; set; }
        public int DaysOverdue { get; set; }
        public string GuardianName { get; set; } = string.Empty;
        public string GuardianPhone { get; set; } = string.Empty;
    }
}
