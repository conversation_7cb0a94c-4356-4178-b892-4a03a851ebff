using System;
using System.Drawing;
using System.Windows.Forms;
using AlNoorEducationalInstitute.Services;
using AlNoorEducationalInstitute.Models;

namespace AlNoorEducationalInstitute.Forms
{
    /// <summary>
    /// نافذة إدارة الطلاب
    /// Student management form
    /// </summary>
    public partial class StudentManagementForm : Form
    {
        private readonly IStudentService _studentService;
        private DataGridView dgvStudents;
        private Panel pnlTop;
        private Panel pnlBottom;
        private TextBox txtSearch;
        private Button btnSearch;
        private Button btnAdd;
        private Button btnEdit;
        private Button btnDelete;
        private Button btnRefresh;
        private Label lblSearch;
        private ComboBox cmbStatus;
        private Label lblStatus;

        public StudentManagementForm(IStudentService studentService)
        {
            _studentService = studentService;
            InitializeComponent();
            SetupForm();
            LoadStudents();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Text = "إدارة الطلاب";
            this.Size = new Size(1000, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.Font = new Font("Tahoma", 10F, FontStyle.Regular);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Top panel
            pnlTop = new Panel
            {
                Height = 60,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(240, 240, 240),
                Padding = new Padding(10)
            };

            // Search label
            lblSearch = new Label
            {
                Text = "البحث:",
                Size = new Size(50, 25),
                Location = new Point(920, 15),
                TextAlign = ContentAlignment.MiddleRight
            };

            // Search textbox
            txtSearch = new TextBox
            {
                Size = new Size(200, 25),
                Location = new Point(710, 15),
                PlaceholderText = "ابحث بالاسم أو الرقم التعريفي"
            };

            // Search button
            btnSearch = new Button
            {
                Text = "بحث",
                Size = new Size(60, 25),
                Location = new Point(640, 15),
                BackColor = Color.FromArgb(70, 130, 180),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            // Status label
            lblStatus = new Label
            {
                Text = "الحالة:",
                Size = new Size(50, 25),
                Location = new Point(580, 15),
                TextAlign = ContentAlignment.MiddleRight
            };

            // Status combobox
            cmbStatus = new ComboBox
            {
                Size = new Size(120, 25),
                Location = new Point(450, 15),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // Add controls to top panel
            pnlTop.Controls.AddRange(new Control[]
            {
                lblSearch, txtSearch, btnSearch, lblStatus, cmbStatus
            });

            // Bottom panel
            pnlBottom = new Panel
            {
                Height = 60,
                Dock = DockStyle.Bottom,
                BackColor = Color.FromArgb(240, 240, 240),
                Padding = new Padding(10)
            };

            // Buttons
            btnAdd = new Button
            {
                Text = "إضافة طالب",
                Size = new Size(100, 35),
                Location = new Point(10, 12),
                BackColor = Color.FromArgb(34, 139, 34),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            btnEdit = new Button
            {
                Text = "تعديل",
                Size = new Size(80, 35),
                Location = new Point(120, 12),
                BackColor = Color.FromArgb(255, 165, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            btnDelete = new Button
            {
                Text = "حذف",
                Size = new Size(80, 35),
                Location = new Point(210, 12),
                BackColor = Color.FromArgb(220, 20, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            btnRefresh = new Button
            {
                Text = "تحديث",
                Size = new Size(80, 35),
                Location = new Point(300, 12),
                BackColor = Color.FromArgb(70, 130, 180),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            // Add buttons to bottom panel
            pnlBottom.Controls.AddRange(new Control[]
            {
                btnAdd, btnEdit, btnDelete, btnRefresh
            });

            // DataGridView
            dgvStudents = new DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize,
                RowHeadersVisible = false
            };

            // Add controls to form
            this.Controls.Add(dgvStudents);
            this.Controls.Add(pnlBottom);
            this.Controls.Add(pnlTop);

            this.ResumeLayout(false);
        }

        private void SetupForm()
        {
            // Setup status combobox
            cmbStatus.Items.Add(new ComboBoxItem("الكل", null));
            cmbStatus.Items.Add(new ComboBoxItem("نشط", StudentStatus.Active));
            cmbStatus.Items.Add(new ComboBoxItem("متخرج", StudentStatus.Graduated));
            cmbStatus.Items.Add(new ComboBoxItem("منقطع", StudentStatus.Dropped));
            cmbStatus.Items.Add(new ComboBoxItem("منقول", StudentStatus.Transferred));
            cmbStatus.SelectedIndex = 0;

            // Setup DataGridView columns
            SetupDataGridViewColumns();

            // Event handlers
            btnSearch.Click += BtnSearch_Click;
            btnAdd.Click += BtnAdd_Click;
            btnEdit.Click += BtnEdit_Click;
            btnDelete.Click += BtnDelete_Click;
            btnRefresh.Click += BtnRefresh_Click;
            txtSearch.KeyPress += TxtSearch_KeyPress;
            cmbStatus.SelectedIndexChanged += CmbStatus_SelectedIndexChanged;
            dgvStudents.CellDoubleClick += DgvStudents_CellDoubleClick;

            // Button styling
            foreach (Button btn in new[] { btnSearch, btnAdd, btnEdit, btnDelete, btnRefresh })
            {
                btn.FlatAppearance.BorderSize = 0;
            }
        }

        private void SetupDataGridViewColumns()
        {
            dgvStudents.Columns.Clear();

            dgvStudents.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "StudentId",
                HeaderText = "المعرف",
                Visible = false
            });

            dgvStudents.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "StudentNumber",
                HeaderText = "الرقم التعريفي",
                Width = 100
            });

            dgvStudents.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "FullNameArabic",
                HeaderText = "الاسم الكامل",
                Width = 200
            });

            dgvStudents.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "DateOfBirth",
                HeaderText = "تاريخ الميلاد",
                Width = 100
            });

            dgvStudents.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Gender",
                HeaderText = "الجنس",
                Width = 80
            });

            dgvStudents.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "GuardianName",
                HeaderText = "ولي الأمر",
                Width = 150
            });

            dgvStudents.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "GuardianPhone",
                HeaderText = "هاتف ولي الأمر",
                Width = 120
            });

            dgvStudents.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CurrentClass",
                HeaderText = "الفصل الحالي",
                Width = 120
            });

            dgvStudents.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Status",
                HeaderText = "الحالة",
                Width = 80
            });

            dgvStudents.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "EnrollmentDate",
                HeaderText = "تاريخ التسجيل",
                Width = 100
            });
        }

        private async void LoadStudents()
        {
            try
            {
                dgvStudents.Rows.Clear();
                
                var students = await _studentService.GetAllStudentsAsync();
                
                foreach (var student in students)
                {
                    var row = new DataGridViewRow();
                    row.CreateCells(dgvStudents);
                    
                    row.Cells["StudentId"].Value = student.StudentId;
                    row.Cells["StudentNumber"].Value = student.StudentNumber;
                    row.Cells["FullNameArabic"].Value = student.FullNameArabic;
                    row.Cells["DateOfBirth"].Value = student.DateOfBirth.ToString("yyyy/MM/dd");
                    row.Cells["Gender"].Value = student.Gender == Gender.Male ? "ذكر" : "أنثى";
                    row.Cells["GuardianName"].Value = student.GuardianName;
                    row.Cells["GuardianPhone"].Value = student.GuardianPhone;
                    row.Cells["CurrentClass"].Value = student.CurrentClass?.ClassName ?? "غير محدد";
                    row.Cells["Status"].Value = GetStatusDisplayName(student.Status);
                    row.Cells["EnrollmentDate"].Value = student.EnrollmentDate.ToString("yyyy/MM/dd");
                    
                    dgvStudents.Rows.Add(row);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الطلاب: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private string GetStatusDisplayName(StudentStatus status)
        {
            return status switch
            {
                StudentStatus.Active => "نشط",
                StudentStatus.Graduated => "متخرج",
                StudentStatus.Dropped => "منقطع",
                StudentStatus.Transferred => "منقول",
                _ => "غير محدد"
            };
        }

        // Event handlers
        private void TxtSearch_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                BtnSearch_Click(sender, e);
                e.Handled = true;
            }
        }

        private void BtnSearch_Click(object sender, EventArgs e) => PerformSearch();
        private void CmbStatus_SelectedIndexChanged(object sender, EventArgs e) => PerformSearch();
        private void BtnRefresh_Click(object sender, EventArgs e) => LoadStudents();

        private void PerformSearch()
        {
            // TODO: Implement search functionality
            MessageBox.Show("سيتم تطبيق وظيفة البحث لاحقاً", "معلومات", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم فتح نافذة إضافة طالب جديد", "معلومات", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (dgvStudents.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار طالب للتعديل", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            MessageBox.Show("سيتم فتح نافذة تعديل بيانات الطالب", "معلومات", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            if (dgvStudents.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار طالب للحذف", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show("هل تريد حذف الطالب المحدد؟", "تأكيد الحذف", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                MessageBox.Show("سيتم تطبيق وظيفة الحذف لاحقاً", "معلومات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void DgvStudents_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                BtnEdit_Click(sender, e);
            }
        }
    }

    /// <summary>
    /// عنصر ComboBox مع قيمة
    /// </summary>
    public class ComboBoxItem
    {
        public string Text { get; set; }
        public object? Value { get; set; }

        public ComboBoxItem(string text, object? value)
        {
            Text = text;
            Value = value;
        }

        public override string ToString() => Text;
    }
}
