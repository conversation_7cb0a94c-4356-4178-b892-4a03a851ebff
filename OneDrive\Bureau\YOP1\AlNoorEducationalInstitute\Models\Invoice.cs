using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AlNoorEducationalInstitute.Models
{
    /// <summary>
    /// نموذج بيانات الفاتورة
    /// Invoice data model
    /// </summary>
    public class Invoice
    {
        /// <summary>
        /// المعرف الفريد للفاتورة
        /// </summary>
        public int InvoiceId { get; set; }

        /// <summary>
        /// رقم الفاتورة (مُولد تلقائياً)
        /// </summary>
        [Required(ErrorMessage = "رقم الفاتورة مطلوب")]
        [StringLength(50, ErrorMessage = "رقم الفاتورة يجب أن يكون أقل من 50 حرف")]
        public string InvoiceNumber { get; set; } = string.Empty;

        /// <summary>
        /// معرف الطالب المرتبط بالفاتورة
        /// </summary>
        [Required(ErrorMessage = "معرف الطالب مطلوب")]
        public int StudentId { get; set; }

        /// <summary>
        /// تاريخ إصدار الفاتورة
        /// </summary>
        [Required(ErrorMessage = "تاريخ الإصدار مطلوب")]
        public DateTime IssueDate { get; set; }

        /// <summary>
        /// تاريخ استحقاق الدفع
        /// </summary>
        [Required(ErrorMessage = "تاريخ الاستحقاق مطلوب")]
        public DateTime DueDate { get; set; }

        /// <summary>
        /// إجمالي مبلغ الفاتورة
        /// </summary>
        [Required(ErrorMessage = "إجمالي المبلغ مطلوب")]
        [Range(0, double.MaxValue, ErrorMessage = "إجمالي المبلغ يجب أن يكون أكبر من الصفر")]
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// المبلغ المدفوع
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "المبلغ المدفوع يجب أن يكون أكبر من أو يساوي الصفر")]
        public decimal PaidAmount { get; set; } = 0;

        /// <summary>
        /// المبلغ المتبقي
        /// </summary>
        public decimal RemainingAmount => TotalAmount - PaidAmount;

        /// <summary>
        /// حالة الفاتورة
        /// </summary>
        [Required(ErrorMessage = "حالة الفاتورة مطلوبة")]
        public InvoiceStatus Status { get; set; }

        /// <summary>
        /// نوع الفاتورة
        /// </summary>
        [Required(ErrorMessage = "نوع الفاتورة مطلوب")]
        public InvoiceType Type { get; set; }

        /// <summary>
        /// الشهر المطبق عليه الفاتورة (للرسوم الشهرية)
        /// </summary>
        public int? ApplicableMonth { get; set; }

        /// <summary>
        /// السنة المطبقة عليها الفاتورة
        /// </summary>
        public int ApplicableYear { get; set; }

        /// <summary>
        /// العام الدراسي
        /// </summary>
        [Required(ErrorMessage = "العام الدراسي مطلوب")]
        [StringLength(20, ErrorMessage = "العام الدراسي يجب أن يكون أقل من 20 حرف")]
        public string AcademicYear { get; set; } = string.Empty;

        /// <summary>
        /// ملاحظات على الفاتورة
        /// </summary>
        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime LastModifiedDate { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أنشأ السجل
        /// </summary>
        public int CreatedByUserId { get; set; }

        /// <summary>
        /// معرف المستخدم الذي عدل السجل آخر مرة
        /// </summary>
        public int LastModifiedByUserId { get; set; }

        // Navigation Properties
        public virtual Student? Student { get; set; }
        public virtual ICollection<InvoiceItem> InvoiceItems { get; set; } = new List<InvoiceItem>();
        public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();
    }

    /// <summary>
    /// تعداد حالة الفاتورة
    /// </summary>
    public enum InvoiceStatus
    {
        Draft = 1,          // مسودة
        Issued = 2,         // مُصدرة
        PartiallyPaid = 3,  // مدفوعة جزئياً
        FullyPaid = 4,      // مدفوعة بالكامل
        Overdue = 5,        // متأخرة
        Cancelled = 6       // ملغاة
    }

    /// <summary>
    /// تعداد نوع الفاتورة
    /// </summary>
    public enum InvoiceType
    {
        Monthly = 1,        // شهرية
        Registration = 2,   // تسجيل
        Transportation = 3, // نقل
        Books = 4,          // كتب
        Activities = 5,     // أنشطة
        Other = 99          // أخرى
    }
}
